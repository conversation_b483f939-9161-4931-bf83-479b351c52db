 <?php
include('header.php');

$NewSeariesEffectivity = '20240701';

$IsShowButton = true;
if(isset($_REQUEST['MyBizURLBillViewAPIKeyID']) && isset($_REQUEST['MyBizURLBillViewAPIKey'])) {
  
  if(!validateAPIAccess($_REQUEST['MyBizURLBillViewAPIKeyID'],$_REQUEST['MyBizURLBillViewAPIKey'])) {
    include('session.php');
  } else {
    $IsShowButton = false;
  }
} else {
  include('session.php');
}

	if(isset($_POST['savenote']) && $_POST['savenote']=='568gr5gh6790jk') {
		$savenote=FieldDBInput(strtoupper($_POST['note'])); 
		$query=doQuery("SELECT * FROM web_bill_accounts WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."'");
		if(mysql_num_rows($query) > 0) {
			$query=doQuery("UPDATE web_bill_accounts SET note='".$savenote."' WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."'");
		$save="success";
		} else {
			$save="failed";
		}
		if($save=="success") {
			echo "<script>\n<!--\n$(document).ready(function(){\n";
			echo "eModal.alert('Successfully Saved.');";
			echo "\n//-->\n});\n</script>\n";
		} else {
			echo "<script>\n<!--\n$(document).ready(function(){\n";
			echo "eModal.alert('Saving failed.');";
			echo "\n//-->\n});\n</script>\n";
		}
	} 

  $query = doQuery("SELECT ba.*,CONCAT(eb.firstname, ' ',eb.lastname) as encodedbyName,
				           CONCAT(cb.firstname, ' ',cb.lastname) as checkedbyName,
						   CONCAT(ab.firstname, ' ',ab.lastname) as approvedbyName, billtoshipper ,
						     ba.note, other_charge_amount, other_charge_desc, nvt_charge_amount, nvt_charge_desc,
						     ba.contact_billing,
						     ba.IsNewSeriesInvoice 
				   FROM web_bill_accounts ba ".
				   "LEFT JOIN web_webusers eb ON eb.user=ba.encodedby ".
				   "LEFT JOIN web_webusers cb ON cb.user=ba.checkedby ".
				   "LEFT JOIN web_webusers ab ON ab.user=ba.approvedby ".
				   "WHERE ba.bill_magic='".(int)$_REQUEST['billshipmentmagic']."'");
  $InvInfo = mysql_fetch_array($query);
  $IsNewSeriesInvoice = $InvInfo['IsNewSeriesInvoice'];
  $note = $InvInfo['note'];
  $vatcharge = $InvInfo['vat'];
  $lastVTRefNum = $InvInfo['billvt_refnum'];
  $account = $InvInfo['account'];
  $billtoshipper = $InvInfo['billtoshipper'];
  $other_charge_amount = $InvInfo['other_charge_amount'];
  $other_charge_desc = $InvInfo['other_charge_desc'];
  $nvt_charge_amount = $InvInfo['nvt_charge_amount'];
  $nvt_charge_desc = $InvInfo['nvt_charge_desc']; 
  $contact_billing = $InvInfo['contact_billing']; 
  
	if(trim($billtoshipper)!='') {
		$query=doQuery("SELECT ws.business_style,ws.account,ws.shipper as name,ws.street,ws.brgymun,ws.cityprov,ws.country,ws.zipcode,ws.tino as tin_no,wm.vat as vat,
                 mf.Others_EWT 
									 FROM web_shippers ws
          left join web_mf_accounts wm on ws.account=wm.account
          left join mf_accounts mf on mf.account=ws.account
          WHERE ws.account='".$account."' AND UPPER(TRIM(ws.shipper))='".FieldDBInput(strtoupper(trim($billtoshipper)))."'");
 	$clientInfo=mysql_fetch_array($query);
	} else {
		$query=doQuery("SELECT * FROM mf_accounts mf 
										LEFT JOIN  web_mf_accounts wmf ON mf.account=wmf.account AND mf.hubcode=wmf.hubcode 
										WHERE mf.account='".$account."'");
		$clientInfo=mysql_fetch_array($query);
	}
  
  $query = doQuery("SELECT min(IF((ISNULL(tfs.pickupdate)) or (trim(tfs.pickupdate) = '0000-00-00'),tfs.rcvdate,tfs.pickupdate)) as minDate,
		              	   max(IF((ISNULL(tfs.pickupdate)) or (trim(tfs.pickupdate) = '0000-00-00'),tfs.rcvdate,tfs.pickupdate)) as maxDate,
			               min(tfs.poddate) as mindelDate,
      			 		   max(tfs.poddate) as maxdelDate
		           FROM web_bill_shipment bs
								 LEFT JOIN web_tf_shipments wtfs ON bs.shipment_magic=wtfs.shipment_magic 
  		        	 LEFT JOIN tf_shipments tfs on tfs.billed=wtfs.shipment_magic 
							   WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."'");
  $minmaxPickupDate=mysql_fetch_array($query);

?>
<style>
<!--  
body {
  margin: 0 auto;
  font-family: Arial, Verdana, sans-serif;
  font-size: 14px;
}
.style3{color: #FFFFFF}
<?php if(IsClient()) { ?>
#maincontainer{ text-align:center; vertical-align:top; height: 1056px; width:816px; margin: 0 auto;border:2px solid #ccc;}
<?php } else { ?>
#maincontainer{ text-align:center; vertical-align:top; height: 1056px; width:100%; margin: 0 auto;}
<?php } ?>
#maincontainer > table { margin-left: 30px;}
#printpagebutton{margin: 0 auto;text-align:center;}
.tabledetails { margin-top: 10px }
.tabledetails td{text-align:left}
@media print {
    body.modal-open .modal .modal-header,
    body.modal-open .modal .modal-body {
        visibility: hidden; /*hide modal body and header */
    }
	#myModal{display: none;}
}
//-->
</style>
<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        //Print the page content
		
		alert("Please use the billing invoice #<?php echo $lastVTRefNum ?>\n\nOS: Windows XP (Stand Alone/Virtual) (recommended)\nBrowser: Firefox v43 (recommeded)\n"+
			  "Note: It works also on different OS and Browser\n\nPaper Size: Letter\n\nMargins (Inches)\nLeft: 0.4\nRight: 0.3\nTop: 0.2\nBottom: 0.5\n");
		
        window.print()
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
    }
	
	
//-->	
</script>
<body>

<?php if(IsClient()) { ?>
<br />
<br />
<?php } else {
if($IsShowButton) {
?>
<div id="printpagebutton">
<input type="button" value="Print Billing Invoice Number (<?php echo $lastVTRefNum ?>)" onClick="printpage()"/>
<input type="button" data-toggle="modal" value="Add Note" data-target="#myModal"/>
</div>
<!-- Trigger the modal with a button -->
<?php } ?>
<!-- Modal -->
<div id="myModal" class="modal fade" role="dialog">
  <div class="modal-dialog">

    <!-- Modal content-->
		<div class="modal-content">
		  <div class="modal-body">
			<form method="post" action="bill_print.php">
				<input type="hidden" name="billshipmentmagic" value="<?php echo $_REQUEST['billshipmentmagic'] ?>">
				<div class="form-group">
					<label for="comment">Note:</label>
					<textarea style="text-transform: uppercase;" class="form-control" name="note" value="<?php echo (isset($_POST['note'])) ? $_POST['note'] : $note ?>" rows="5" id="comment"></textarea>
				</div>
				<button type="submit" align="center" name="savenote" value="568gr5gh6790jk" class="btn btn-default">Save</button>
			</form>
		  </div>
		  <div class="modal-footer">
			<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
		  </div>
		</div>

  </div>
</div>
<br /><br />
<?php 
$browser = get_browser(null,true);
if(strtolower(trim($browser['platform'])) != 'winxp') {
?>
<br />
<br />
<br />
<br />
<br />
<?php }
}
?> 
<!---- Client  style="border: 1px solid red" --->

<div id="maincontainer" >
<?php if(IsClient()) { ?><br/><br/><?php } ?>

<table border="0" cellpadding="0" cellspacing="0" width="95%" style="position:relative; top: 10px;">
	<tr>
		<td width="150" rowspan=4>&nbsp;</td>
		<td colspan="6" align="left"><?php echo $clientInfo['name'] ?></td>
	</tr>
	<tr>
		<td colspan="6">&nbsp;</td>
	</tr>
	<tr>
		<?php
		$billdate = str_replace(" ","&nbsp;",FormatDate($InvInfo['bill_date'],3));
		?>
		<td width="500" align="left" colspan="3"><?php echo $clientInfo['street'] ?>&nbsp;<?php echo $clientInfo['brgymun'] ?>&nbsp;<?php echo $clientInfo['cityprov'] ?></td>
		<td rowspan="2" width="460">&nbsp;</td>
		<?php $queryb = doQuery("SELECT wtfs.shipment_magic,wtfs.trip_number,wtfs.gatepassno
					 FROM web_bill_shipment wbs
					 LEFT JOIN web_tf_shipments wtfs ON wbs.shipment_magic=wtfs.shipment_magic 
					 LEFT JOIN tf_shipments tfs on tfs.billed=wtfs.shipment_magic 
					 WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."' AND tfs.billed=wtfs.shipment_magic");
				$result=mysql_fetch_array($queryb);
		?>
		<td align="right" valign="bottom" width="200">&nbsp;&nbsp;<?php echo $billdate ?><?php if($account=='SPI' || $account=='HPI' || $account=='MNC') { ?></br>TRIP NO. : <?php echo $result['trip_number']; ?><?php } ?></td>
	</tr>
	<tr>
		<td width="180" align="left"><?php echo $clientInfo['tin_no'] ?></td>
		<td width="100"></td>
		<td width="220" style="position: relative;">&nbsp;<div style="width: 250px;position: absolute;top: 0;"align="left"><?php echo $clientInfo['business_style']<>'' ? $clientInfo['business_style'] :  $clientInfo['name'];  ?></div></td>
	    <td align="right" >&nbsp;&nbsp;<?php echo $clientInfo['account'] ?></td>
	</tr>
</table>

<br />
<br />

<!----- Billing --->

<table border="0" cellpadding="0" cellspacing="0" width="100%">
	<tr>
		<td valign="top" height="440" colspan=2><?php //charges details & location and amount ?>
		<?php //charges details ?>
		<br>
		<br>
<table cellpadding="0" class="tabledetails" cellspacing="0" border="0" width="85%">
<?php
$IsBillSCGOutBound = false;
if($account=='SCG') {
	$y = substr($InvInfo['bill_date'],0,4);
	$query = doQuery("SELECT tfs.category as category  
		                FROM web_bill_shipment bs
						LEFT JOIN web_tf_shipments wtfs ON bs.shipment_magic=wtfs.shipment_magic 
  		        	    LEFT JOIN tf_shipments tfs on tfs.billed=wtfs.shipment_magic 
							      WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."' GROUP BY tfs.category");
$category=mysql_fetch_array($query);
if(mysql_num_rows($query)==1 && $category['category']!='' && $category['category']=='OUTBOUND') {
	$IsBillSCGOutBound = true;
?>
<tr><td colspan="2" height="40">
This is to bill your account for <b><?php echo $category['category'] ?></b> trucking services. Details are the following:
</td></tr>
<?php
} else {
?>
<?php	
	$query = doQuery("SELECT wtfs.job_reference,wtfs.orderno,wtfs.bl_number 
		                FROM web_bill_shipment bs
								    LEFT JOIN web_tf_shipments wtfs ON bs.shipment_magic=wtfs.shipment_magic 
  		        	    LEFT JOIN tf_shipments tfs on tfs.billed=wtfs.shipment_magic 
							      WHERE billed='".(int)$_REQUEST['billshipmentmagic']."' GROUP BY wtfs.job_reference,wtfs.orderno,wtfs.bl_number");
?>	
<tr><td colspan="2" height="40">This is to  bill you for trucking and hauling services for:
<br><br>
<table cellpadding="2" cellspacing="5" border="" width="60%">
	<tr><td><b>JOB REFERENCE</b></td><td><b>TRANSACTION CODE</b></td><td><b>BL NUMBER</b></td></tr>
<?php
	while($orderno=mysql_fetch_array($query)) {
?>
	<tr><td width="250"><?php echo $orderno['job_reference'] ?></td><td><?php echo $orderno['orderno'] ?></td><td><?php echo $orderno['bl_number'] ?></td></tr>
	<?php
	}
?>
</table>
</td></tr>
<?php
}
} else if($account=='SPI' || $account=='MNC' || $account=='HPI') {
	$query=doQuery("SELECT wtfsp.grgi_no,wtfs.add_qty_units,wtfs.shipper_cityprov,tfs.cityprov,tfs.saddress,tfd.plateno,wtfs.waybillno,wtfs.gatepassno,wtfs.shipment_magic,wtfs.offline_waybillno,wtfs.driver,tfs.rcvqty,tfs.consignee,tfs.shipper,tfs.poddate,tfs.billcode,wms.Code,wms.UOM_travel_desc
				    FROM web_bill_shipment wbs
					LEFT JOIN tf_shipments tfs on tfs.billed=wbs.shipment_magic
					LEFT JOIN web_tf_shipments wtfs ON wbs.shipment_magic=wtfs.shipment_magic 
					LEFT JOIN web_mode_shipments wms ON wbs.billtype=wms.Code
					LEFT JOIN tf_dispatch tfd on tfd.drno=tfs.drno
					LEFT JOIN web_tf_shipment_package wtfsp ON wtfsp.shipment_magic=wtfs.shipment_magic
					WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."'");
	$shipmentmode=mysql_fetch_array($query);
	
	if($shipmentmode['add_qty_units']<>'') {
		$totalpkgs=$shipmentmode['rcvqty']+$shipmentmode['add_qty_units'];
	} else {
		$totalpkgs += $shipmentmode['rcvqty'];
	}
?>
<tr><td colspan="2" height="40">TO BILL YOU FOR MOTORCYCLE DELIVERY:</td></tr>
<tr><td>MODE OF SHIPMENT :</td><td><?php echo $shipmentmode['UOM_travel_desc']; ?></td></tr>
<tr><td width="250">PICK-UP&nbsp;DATE :</td><td><?php echo ($minmaxPickupDate['minDate']<>$minmaxPickupDate['maxDate']) ? FormatDate($minmaxPickupDate['minDate'],3) : FormatDate($minmaxPickupDate['minDate'],3) ?></td></tr>
<tr><td>DELIVERY DATE :</td><td><?php echo FormatDate($shipmentmode['poddate'],3); ?></td></tr>
<tr><td>PICK UP POINT :</td><td><?php echo $shipmentmode['shipper'].' - '.$shipmentmode['shipper_cityprov']; ?></td></tr>
<tr><td>CONSIGNEE :</td><td><?php echo $shipmentmode['consignee'].' - '.$shipmentmode['cityprov']; ?></td></tr>
<?php
    $query=doQuery("SELECT tfs.consignee,wbs.addon_unit_rate_multiplier_value,wbs.addon_unit_rate_multiplier_uom,tfs.brgymun,tfs.waybillno,wtfs.add_qty_units,wtfs.shipment_magic,wtfs.offline_waybillno,tfs.rcvqty
				    FROM web_bill_shipment wbs
					LEFT JOIN tf_shipments tfs on tfs.billed=wbs.shipment_magic
					LEFT JOIN web_tf_shipments wtfs ON wbs.shipment_magic=wtfs.shipment_magic
					WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."' AND wbs.shipment_magic=wtfs.shipment_magic ORDER BY tfs.waybillno");
	$hwd = '';
	$totalqty = 0;
	while($rows=mysql_fetch_array($query)) {
		if($rows['offline_waybillno'] > 0) {
			$hwd .= $rows['offline_waybillno'].' ('.$rows['rcvqty'];
		} else {
			$hwd .= $rows['waybillno'].' ('.$rows['rcvqty'];
		}
		if($rows['rcvqty'] > 1) { $hwd .= ' UNITS) '; } else { $hwd .= ' UNIT) '; }
		if($account=='MNC' && ($rows['add_qty_units']<>'' && (int)$rows['add_qty_units'] > 0)) { $hwd .= ' + '.round($rows['addon_unit_rate_multiplier_value']).' '.$rows['addon_unit_rate_multiplier_uom']; }
		if($account=='MNC' && $rows['waybillno'] > 1) { $hwd .= ' - '.$rows['consignee']; }
		$hwd .= '</br>';
		$totalqty += ($rows['rcvqty'] + $rows['addon_unit_rate_multiplier_value']);
	}
?>
<tr><td>TOTAL PKGS :</td><td><?php echo $totalqty; ?> <?php if($totalqty > 1) { ?>UNITS <?php } else { ?> UNIT <?php } ?></td></tr>
<tr><td valign="top">HWB NO. :</td><td valign="top"><?php echo $hwd ?></td></tr>

<?php if($account=='HPI' || $account=='SPI') { ?>
<tr>
	<?php if($account=='SPI') { ?>
	<td>DR NO. :</td>
	<?php } elseif($account=='HPI') { ?>
	<td>MT/DA NO. :</td>
	<?php } ?>
	<td><?php $querya=doQuery("SELECT wtfsp.packageno,wtfs.shipment_magic,wtfsp.grgi_no,tfs.waybillno
					 FROM web_bill_shipment wbs
					 LEFT JOIN tf_shipments tfs on tfs.billed=wbs.shipment_magic
					 LEFT JOIN web_tf_shipments wtfs ON wbs.shipment_magic=wtfs.shipment_magic 
					 LEFT JOIN web_tf_shipment_package wtfsp ON wtfsp.shipment_magic=wtfs.shipment_magic
					 WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."' AND wtfsp.shipment_magic=wtfs.shipment_magic ORDER BY tfs.waybillno");
					 $rowcount=0;
					 $countable_danum = 0;
					 $data = array();
					 while($rows=mysql_fetch_array($querya)){
						$trim_danum=trim($rows['packageno']);
						if(!empty($trim_danum)){
							$data[$countable_danum]['packageno']=$trim_danum;
							$countable_danum++;
						}
					 }
					 for($i=0;$i<$countable_danum;$i++){
						$comma = ", ";
						if($countable_danum==1){
							$comma = "";
						}else if($i+1 < $countable_danum){
								$comma = ", ";
						}else{
							$comma = "";
						}
						echo "".$data[$i]['packageno']."".$comma;
					 }
		?>
	</td>
</tr>
<?php } ?>
<?php if($account=='SPI') { ?>
<tr><td>DRIVER'S NAME :</td>
	<td><?php $querya=doQuery("SELECT wtfs.shipment_magic,wtfs.driver
					 FROM web_bill_shipment wbs
					 LEFT JOIN web_tf_shipments wtfs ON wbs.shipment_magic=wtfs.shipment_magic 
					 LEFT JOIN tf_shipments tfs on tfs.billed=wtfs.shipment_magic 
					 WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."' AND tfs.billed=wtfs.shipment_magic LIMIT 1");
					 while($rows=mysql_fetch_array($querya)){
						echo $rows['driver'];
						echo ' ';
						}
		?>
		</td>
</tr>
<?php } ?>
<tr><td>GRGI SLIP NO. :</td>
	<td>
		<?php
		$querya=doQuery("SELECT GROUP_CONCAT(DISTINCT wtfsp.grgi_no) as grgi_no 
							 FROM web_bill_shipment wbs
							 LEFT JOIN tf_shipments tfs on tfs.billed=wbs.shipment_magic
							 LEFT JOIN web_tf_shipments wtfs ON wbs.shipment_magic=wtfs.shipment_magic 
							 LEFT JOIN web_tf_shipment_content wtfsp ON wtfsp.shipment_magic=wtfs.shipment_magic
							 WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."' AND wtfsp.shipment_magic=wtfs.shipment_magic ORDER BY tfs.waybillno");
		while($rows=mysql_fetch_array($querya)){
			if(trim($rows['grgi_no'])!='') {
				$grgino_list=$rows['grgi_no'];
			}
		} 
		$stringexplode=explode(',', $grgino_list);
		$countstringexplode = count($stringexplode);
		foreach($stringexplode as $key => $val)
		{
		    $keyIncrement = $key+1;
      if(trim($val)!='') {
       echo $val.($countstringexplode == $keyIncrement ? "" : ",") ;
       if(($keyIncrement) % 5 == 0)
       echo "<br>";
      }
		}
		?>
	</td>
</tr>

<?php if($account=='HPI' && $shipmentmode['gatepassno']<>'') { ?>
<tr><td>GATEPASS NO. :</td>
	<td>
		<?php $querya=doQuery("SELECT wtfs.shipment_magic,wtfs.gatepassno,tfs.waybillno
					 FROM web_bill_shipment wbs
					 LEFT JOIN web_tf_shipments wtfs ON wbs.shipment_magic=wtfs.shipment_magic 
					 LEFT JOIN tf_shipments tfs on tfs.billed=wtfs.shipment_magic 
					 WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."' AND wbs.shipment_magic=wtfs.shipment_magic ORDER BY tfs.waybillno");
					 while($rows=mysql_fetch_array($querya)){
						if($rows['gatepassno']<>'') {
							echo $rows['gatepassno'];
						} else {
							echo 'NO GATEPASS';
						}
						echo ',';
					}
		?>
	</td>
</tr>
<?php } ?>
<tr>
	<td>
		<?php if($account=='SPI' || $account=='HPI') { ?> DELIVERED BY :<?php } else if($account=='MNC') { ?>PLATE NO. :<?php } ?>
	</td>
	<td><?php echo $shipmentmode['plateno']; ?></td>
</tr>
<?php if($note<>'') { ?>
<tr><td>NOTE :</td><td><?php echo $note; ?></td></tr>
<?php } } else { ?>
<?php if($account!='PRIMER'){ ?>
	<?php 
	$month=StrToUpper(FormatDate($InvInfo['bill_date'],1));
	if($account=='SCHARLAB'){
		$splitdate=explode('/',FormatDate($minmaxPickupDate['maxDate'],1));
		$month=StrToUpper($splitdate[0]);
	}
	?>
	<tr><td colspan="2" height="40">ATTENTION: <?php echo $contact_billing ?></td></tr>
<tr><td colspan="2" height="">TO BILL YOU FOR DELIVERY CHARGES FOR THE MONTH OF <?php echo $month ?></td></tr>
<?php } else { ?>
<tr><td colspan="2" height="40">TO BILL YOU FOR DELIVERY CHARGES FOR THE TRANSACTION OF <?php echo ($minmaxPickupDate['minDate']<>$minmaxPickupDate['maxDate']) ? FormatDate($minmaxPickupDate['minDate'],2).'&nbsp;to&nbsp;'.FormatDate($minmaxPickupDate['maxDate'],2) : FormatDate($minmaxPickupDate['minDate']) ?></td></tr>
<?php }
if($account!='SCHARLAB') {
?>
<tr><td width="200">PICK-UP&nbsp;DATE</td><td>:&nbsp;<?php echo ($minmaxPickupDate['minDate']<>$minmaxPickupDate['maxDate']) ? FormatDate($minmaxPickupDate['minDate'],2).'&nbsp;to&nbsp;'.FormatDate($minmaxPickupDate['maxDate'],2) : FormatDate($minmaxPickupDate['minDate']) ?></td></tr>
<?php
}
$queryCat = doQuery("SELECT DISTINCT TRIM(UPPER(tfs.category)) as category  
		           FROM web_bill_shipment bs
								 LEFT JOIN web_tf_shipments wtfs ON bs.shipment_magic=wtfs.shipment_magic 
  		        	 LEFT JOIN tf_shipments tfs on tfs.billed=wtfs.shipment_magic 
							 WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."'");
$catdesc = '';
while($row=mysql_fetch_array($queryCat)) {
	if($catdesc!='' && trim($row['category'])!='')
	  $catdesc =  ', '.$row['category'];
	else
	  $catdesc =  trim($row['category']);
}
if($catdesc<>'') {
	if($account=='DVSI') {
?>
<tr><td>PLATE NUMBER</td><td>:&nbsp;<?php echo $catdesc ?></td></tr>
<?php
	} else {
?>
<tr><td>CATEGORY</td><td>:&nbsp;<?php echo $catdesc ?></td></tr>
<?php }
}

?>
<tr><td  colspan=2 height="40">(Please see attachment of shipment details & charges)</td></tr>

<tr><td height="40" colspan=2><u>CHARGES SUMMARY</u></td></tr>
<?php } ?>
</table> <?php // end charges details ?>
  <?php // location and charges ?>
  <table cellpadding="0" border="0" cellspacing="0" width="85%">
  <?php
  //SCG OUTBOUND
  if($IsBillSCGOutBound) {

	$query = doQuery("SELECT tfs.cityprov as location,wtfs.shipment_magic as shpmagic,total_rate as total_charge
									  FROM web_bill_shipment bs
  			            LEFT JOIN web_tf_shipments wtfs ON bs.shipment_magic=wtfs.shipment_magic 
  		        			LEFT JOIN tf_shipments tfs on tfs.billed=wtfs.shipment_magic  
							      WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."' ORDER BY tfs.cityprov");
  $x = 0;
  $billshipmentrecords = array();
  while($items = mysql_fetch_array($query)) {
    $billshipmentrecords[$x] = array('shpid' => $items['shpmagic'],
  		                             'location' => $items['location'],
									 'total_charge' => $items['total_charge']);
	if(!in_array($items['location'],$locationlist))
	  $locationlist[] = $items['location'];
    $x++;
  }
?>

<tr>
<td align="left"><b>DELIVERY DESTINATION</b></td>
<td align="left"><b>NO. OF TRIPS</b></td>
<td align="right"><b>AMOUNT</b></td>
</tr>
 <?php
  $query=doQuery("SELECT tfs.cityprov as location,
				         wtfs.shipment_magic as shipment_magic 
				  FROM web_bill_shipment bs
				  LEFT JOIN web_tf_shipments wtfs ON bs.shipment_magic=wtfs.shipment_magic 
  		          LEFT JOIN tf_shipments tfs on tfs.billed=wtfs.shipment_magic 
				  WHERE bs.bill_magic='".(int)$_REQUEST['billshipmentmagic']."' GROUP BY tfs.waybillno ORDER BY TRIM(tfs.cityprov)");

  $x = 0;
  while($location=mysql_fetch_array($query)) {
	   $location_str_list[$x]['location']= $location['location'];
	   $location_str_list[$x]['shipment_magic'] = $location['shipment_magic'];
	   $x++;
  }

  for($x = 0; $x < sizeof($location_str_list); $x++) {
     if(!in_array($location_str_list[$x]['location'],$location_list_rec['location'])) {
		$location_list_rec['location'][] = $location_str_list[$x]['location'];
	 }
  }
  
  for($x = 0; $x < sizeof($location_list_rec['location']); $x++) {
	$waybillstr = '';
	for($z = 0; $z < sizeof($location_str_list); $z++) {
	  if($location_str_list[$z]['location']==$location_list_rec['location'][$x]) {
		if($waybillstr!='') {
		   $waybillstr .= ','.$location_str_list[$z]['shipment_magic'];
		} else {
		   $waybillstr = $location_str_list[$z]['shipment_magic'];
		}
	  }
	}
    $location_list_rec['shipment_magic'][$x] = $waybillstr;
  }
	
  $total_other_charges = 0;
  $counter_other = 0;
  $g_total_location_total_rate = 0;
  for($z = 0; $z < sizeof($location_list_rec['location']); $z++) {
	    if(trim($location_list_rec['location'][$z])=='') CONTINUE;
	    $queryl=doQuery("select IF((ISNULL(tfs.pickupdate)) or (trim(tfs.pickupdate) = '0000-00-00'),tfs.rcvdate,tfs.pickupdate) as pickup_date,	
							bs.waybillno as waybill_no,
							bs.orderno as order_number,
							tfs.consignee as consignee,
							tfs.shipper as shipper,
							tfs.content as content,
							wtfs.bl_number as bl_number,
							tfd.plateno as plateno,
							mship.UOM_Description as unit_measurement,
							bs.declare_value as declare_value,
							bs.valuation_rate as valuation_rate,
							ship.mode_shipment as modeshipment,
							bs.chargeable_weight as chargeable_weight,
							bs.unit_rate as unit_rate,
							ROUND(IF(tfs.wgtvolume > 0,(tfs.wgtvolume/".VOLUME_METRIC_CBM_DIVISOR."),((tfs.len/".CBM_LENGTH_WIDTH_HEIGHT_DIVISOR.")*(tfs.wid/".CBM_LENGTH_WIDTH_HEIGHT_DIVISOR.")*(tfs.hgt/".CBM_LENGTH_WIDTH_HEIGHT_DIVISOR."))),3) as cbm,
						    CONCAT(tfs.len,' x ', tfs.wid,' x ',tfs.hgt) as measurement,
						    ROUND(IF(tfs.wgtvolume > 0,(tfs.wgtvolume/".VOLUME_METRIC_DIVISOR."),((tfs.len*tfs.wid*tfs.hgt)/".VOLUME_METRIC_DIVISOR.")),2) as volumetric_charge,
						    ROUND(IF(tfs.wgtvolume > 0, IF((tfs.wgtvolume/".VOLUME_METRIC_DIVISOR.") > tfs.wgt,(tfs.wgtvolume/".VOLUME_METRIC_DIVISOR."),tfs.wgt),
					  					  IF(((tfs.len*tfs.wid*tfs.hgt)/".VOLUME_METRIC_DIVISOR.") > tfs.wgt,((tfs.len*tfs.wid*tfs.hgt)/".VOLUME_METRIC_DIVISOR."),tfs.wgt)),2) as chargable_weight,
							bs.rate as rate,
							tfs.rcvqty as pcount,
							wtfs.isChargeperqty as isChargeperqty,
							wtfs.is_podonfile as is_podonfile,
							bs.total_rate as total_rate,
							tfs.receivedby as receivedby,
							tfs.poddate as datereceived,
							bs.packingcrating_fee as packingcrating_fee,
							bs.handlinginout_fee as handlinginout_fee,
							bs.outsidecitylimit_fee as outsidecitylimit_fee,
							bs.rushshipment_fee as rushshipment_fee,
							bs.othercharges_fee as othercharges_fee,
							wtfsa.magic_id as ImageID,
							wtfs.shipment_magic as shpid 
						FROM 
							web_bill_shipment bs
							LEFT JOIN web_tf_shipments wtfs ON bs.shipment_magic=wtfs.shipment_magic 
  		                    LEFT JOIN tf_shipments tfs on tfs.billed=wtfs.shipment_magic  
							LEFT JOIN tf_dispatch tfd on tfd.drno=tfs.drno 	
							LEFT JOIN web_mode_shipments mship on mship.Code=tfs.billcode   
							LEFT JOIN mf_consignees con on con.consignee=tfs.consignee AND con.account=tfs.account 
							LEFT JOIN web_mode_shipments ship on ship.Code=tfs.billcode
							LEFT JOIN web_trans_attachment wtfsa on wtfsa.attachment_transID=wtfs.shipment_magic AND transtype=".TRANSTYPE_SHIPMENT." AND attachmenttype=".TRANSTYPE_ATTACHMENT_POD." 
						WHERE (TRIM(UPPER(tfs.cityprov))='".FieldDBInput(trim(strtoupper($location_list_rec['location'][$z])))."') AND
						(wtfs.shipment_magic IN (".$location_list_rec['shipment_magic'][$z].")) AND 
					 	bs.bill_magic='".(int)$_REQUEST['billshipmentmagic']."'  
						GROUP BY bs.shipment_magic");

  
      $total_location_total_rate = 0;
	  $counter = 0;
	  while($row=mysql_fetch_array($queryl)) {
	    $total_location_total_rate += $row['rate'];
		$g_total_location_total_rate += $row['rate'];
		$total_other_charges += $row['othercharges_fee'];
		if($row['othercharges_fee'] > 0) {
			$counter_other++;
		}
	    $counter++;
	  }
       ?>
      <tr>
<td align="left"><?php echo trim(strtoupper($location_list_rec['location'][$z])) ?></td>
<td align="left"><?php echo $counter ?></td>
<td align="right"><?php echo number_format($total_location_total_rate,2) ?></td>
</tr>
  <?php } ?>
  
<tr>
<td align="left" colspan=3><br></td></tr>
<tr>  
<td align="left" colspan=3><b>OTHERS:</b></td></tr>
<tr>
<td align="left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;MANUAL UNLOADING</td>
<td align="left"><?php echo $counter_other ?></td>
<td align="right"><?php echo number_format($total_other_charges,2) ?></td>
</tr>
<?php
$rst['TotalCharges'] = $g_total_location_total_rate + $total_other_charges;
  } else { //END SCG OUTBOUND 
$query = doQuery("SELECT sum(rate) as ServiceCharges, sum(valuation_rate) as TotalValuationFee, sum(packingcrating_fee) as TotalPackingCratingFee,
								         sum(handlinginout_fee) as TotalHandlingFee, sum(outsidecitylimit_fee) as OutsideCityFee, sum(othercharges_fee) as OtherCharges,
												 sum(total_rate) as TotalCharges 
								  FROM web_bill_shipment
							    WHERE bill_magic='".(int)$_REQUEST['billshipmentmagic']."'");
$rst = mysql_fetch_array($query)
?>
<?php if($account!='SPI' && $account!='HPI' && $account!='MNC') {
	if($account=='DVSI' && $other_charge_amount > 0) {
	  $rst['ServiceCharges'] += $other_charge_amount;
	  $rst['TotalCharges'] += $other_charge_amount;
    }
	?>
<tr>
<td align="left"><?php if($account=='LDS' || $account=='MONARK' || $account=='AGA') { ?>FREIGHT FEE :<?php } else { ?>SERVICE FEE  :<?php } ?></td>
<td align="right"  width="150"><?php echo number_format($rst['ServiceCharges'],2) ?></td>
</tr>
<?php  } if((float)$rst['TotalValuationFee'] > 0 && $account!='SPI') { ?>
<tr>
<td align="left">VALUATION FEE</td>
<td align="right"  width="150"><?php echo number_format($rst['TotalValuationFee'],2) ?></td>
</tr>
<?php  } if((float)$rst['TotalPackingCratingFee'] > 0) { ?>
<tr>
<?php if($account=='DVSI') { ?>
<td align="left">DEMURRAGE FEE:</td>
<?php } else { ?>
<td align="left">PACKING & CRATING FEE:</td>
<?php } ?>
<td align="right"  width="150"><?php echo number_format($rst['TotalPackingCratingFee'],2) ?></td>
</tr>
<?php  } if((float)$rst['TotalHandlingFee'] > 0) { ?>
<tr>
<td align="left">HANDLING IN & OUT FEE:</td>
<td align="right"  width="150"><?php echo number_format($rst['TotalHandlingFee'],2) ?></td>
</tr>
<?php  } if((float)$rst['OutsideCityFee'] > 0) { ?>
<tr>
<td align="left">OUTSIDE CITY LIMITS FEE:</td>
<td align="right"  width="150"><?php echo number_format($rst['OutsideCityFee'],2) ?></td>
</tr>
<?php 
} if((float)$rst['OtherCharges'] > 0) { ?>
<tr>
<td align="left">OTHER CHARGES FEE</td>
<td align="right"  width="150"><?php echo number_format($rst['OtherCharges'],2) ?></td>
</tr>
<?php 
}
} 
if($account!='DVSI' && $other_charge_amount > 0) {
	$rst['TotalCharges'] += $other_charge_amount;
	?>
<tr>
<td align="left"><?php echo strtoupper($other_charge_desc) ?></td>
<td align="right"  width="150"><?php echo number_format($other_charge_amount,2) ?></td>
</tr>
<?php } 
	if($nvt_charge_amount > 0) {
	?>
<tr>
<td align="left"><?php echo strtoupper($nvt_charge_desc) ?></td>
<td align="right"  width="150"><?php echo number_format($nvt_charge_amount,2) ?></td>
</tr>
<?php } ?>

</table><?php //end location and charges ?>




</td><?php //end charges & details & location and amount ?>
</tr>



<tr>
<?php
if($account=='SPI' || $account=='HPI' || $account=='MNC') { ?>
	<td colspan="2" height="50"><?php //VAT and Total ?>
<?php } else { ?>
	<td colspan="2" height="100"><?php //VAT and Total ?>
<?php
}
?>

<?php $gt_total_charges = round(($rst['TotalCharges'] * ((100+$clientInfo['vat']) / 100)),2);
$vt_total_charges = $gt_total_charges - $rst['TotalCharges'];

if($IsNewSeriesInvoice==1) {


 $total_amount = $rst['TotalCharges'];
	$db_added_vat = $clientInfo['vat'];
 $db_ewt = $clientInfo['Others_EWT'];

 $totalPayment=0;
	$VatSales=0;
	$Vat=0;
	$VatZeroRated=0;
	$NetVatSales = 0;
	$TotalGrossSales = 0;
	$TotalVatGrossSales = 0;
	$EWTAmount = 0;
	
 
 if($db_added_vat > 0) {
		 $NetVatSales = $total_amount;
		 $VatSales=($total_amount * ((100+$db_added_vat)/100));
		 $Vat=($VatSales-$total_amount);
		 $TotalGrossSales = ($NetVatSales + $Vat);
		 $TotalVatGrossSales = ($NetVatSales + $Vat);
		 $EWTAmount = ($NetVatSales * ($db_ewt/100));
	} else	{
		 $totalPayment=$total_amount;
		 $VatExempted=$total_amount;
		 $TotalGrossSales = $VatZeroRated = $total_amount;
		 $EWTAmount = ($VatExempted * ($db_ewt/100));
	}	
	
	$emptystring = "0.00";
	




?>
<style>

</style>
<table  cellpadding="1" border="0"  cellspacing="0" width="95%" style="margin-top:-25px;" id="table_sales">

<tr>
	<td align=right style="width:350px; line-height:1.8;">
	<?php //echo "Vatable Sales"; ?>
	</td>
	<td align=right style="width:250px; line-height:1.8;">
	<?php echo ($NetVatSales > 0) ? number_format($NetVatSales,2) : $emptystring ?> 
	</td>
	<td align=right  style="width:350px; line-height:1.8;">
	<?php //echo "Total Sales" ?>
	</td>
	<td align=right style="line-height:1.8;">
	<?php
	if($TotalVatGrossSales > 0 ) {
		echo number_format($TotalVatGrossSales,2);
	} elseif($VatZeroRated > 0) {
	  echo number_format($VatZeroRated,2);
	} else {
		echo $emptystring;
	} ?>
	</td>
	<td align=right style="width:100px; line-height:1.8;" ></td>
	</tr>
		
  <tr>
	<td align=right>
	<?php //echo "VAT-Exempt Sales" ?>
	</td>
	<td align=right>
	<?php echo ($VatExempted > 0) ? number_format($VatExempted,2) : $emptystring ?>
	</td>
	<td align=right>
	<?php //echo "Less: VAT" ?>
	</td>
	<td align=right>
	<?php echo ($Vat > 0) ? number_format($Vat,2) : $emptystring ?>
	</td>
	<td align=right></td>
	</tr>
	
	<tr>
	<td align=right>
	<?php //echo "Zero-Rated Sales" ?>
	</td>
	<td align=right>
	<?php echo ($VatZeroRated > 0) ? number_format($VatZeroRated,2) : $emptystring  ?>
	</td>
	<td align=right>
	<?php //echo "Amount Net of VAT" ?>
	</td>
	<td align=right>
	<?php echo ($NetVatSales > 0) ? number_format($NetVatSales,2) :  $emptystring ?>
	</td>
	<td align=right></td>
	</tr>
		
	<tr>
	<td align=right>
	<?php //echo "VAT Amount"  ?>
	</td>
	<td align=right>
	<?php echo ($Vat > 0) ? number_format($Vat,2) : $emptystring ?> 
	</td>
	<td align=right>
	<?php //echo "Less: SC/PWD Discount" ?>
	</td>
	<td align=right>
	<?php echo $emptystring  ?>
	</td>
	<td align=right></td>
	</tr>
	
	<tr>
	<td align=right>
	<?php //echo "Total Sales" ?>
	</td>
	<td align=right>
	<?php
	echo ($TotalGrossSales > 0) ?  number_format($TotalGrossSales,2) : $emptystring ;
	?>
	</td>
	<td align=right>
	<?php //echo "Amount Due" ?>
	</td>
	<td align=right>
	<?php if($NetVatSales > 0) {
		 echo number_format($NetVatSales,2);
	} elseif($VatZeroRated > 0) {
		echo number_format($VatZeroRated,2);
	} else {
		echo $emptystring;
	} ?>
	</td>
	<td align=right></td>
	</tr>
	
	<?php /********************** end zero rated **********************/ ?>
	
	<tr>
	<td>
	</td>
	<td>
	</td>
	<td align=right>
	<?php //echo "Less: W/Tax" ?>
	</td>
	<td align=right>
	<?php echo ($EWTAmount > 0) ? number_format($EWTAmount,2) : $emptystring ?>
	</td>
	<td align=right></td>
	</tr>
	
	<tr>
  <td>
	</td>
	<td>
	</td>
	<td align=right>
	<?php //echo "Amount Net of W/Tax" //<?php echo number_format($VatZeroRated,2)  ?>
	</td>
	<td align=right>
	<?php echo (($NetVatSales - $EWTAmount) > 0) ? number_format($NetVatSales - $EWTAmount,2) : $emptystring ?>
	</td>
	<td align=right></td>
	</tr>

	
	<tr>
	<td>
	</td>
	<td>
	</td>
	<td align=right>
	<?php //echo "Add: VAT"  ?>
	</td>
	<td align=right>
	<?php echo ($Vat > 0) ? number_format($Vat,2) : $emptystring ?>
	</td>
	<td align=right></td>
	</tr>
	


	<tr>
	<td>
	</td>
	<td>
	</td>
	<td align=right>
	<?php //echo "Total Amount Due" ?>
	</td>
	<td align=right>
	<b>
	<?php echo "P";
	if($NetVatSales > 0) {
		 echo number_format((($NetVatSales - $EWTAmount) + $Vat),2);
	} elseif($VatZeroRated > 0) {
		echo number_format(($VatZeroRated - $EWTAmount),2);
	} else {
		echo $emptystring;
	} ?>
	</b>	
	</td>
	<td></td>
	</tr>
	
	
	</table>
	
	
	
	
</td>
</tr>	



</table>



<?php
} else {
if($nvt_charge_amount > 0) { 
?>
<table  cellpadding="0" border="0"  cellspacing="0" width="85%">
<tr>
<td align="right"><b>VATable Amount</b>&nbsp;:</td>
<td align="right" width="150">P&nbsp;<?php echo number_format($rst['TotalCharges'],2) ?></td>
</tr>
<tr>
<td  align="right"><b>VAT  </b>&nbsp;:</td>
<td align="right"><?php echo number_format($vt_total_charges,2) ?></td>
</tr>
<tr>
<td align="right"><b>NON-VAT</b>&nbsp;:</td>
<td style="border-bottom: 2px #000 solid" align="right"><?php echo number_format($nvt_charge_amount,2) ?></td>
</tr>
<tr>
<td align="right"><b>Gross Amount</b>&nbsp;:</td>
<td align="right" style="border-bottom: 5px #000 double"><b>P&nbsp;<?php echo number_format(($gt_total_charges+$nvt_charge_amount),2) ?></b></td>
</tr>
</table>
<?php } else { ?>
<table  cellpadding="0" border="0"  cellspacing="0" width="85%">
<tr>
<td align="right"><b>VATable Amount </b>&nbsp;:</td>
<td align="right" width="150">P&nbsp;<?php echo number_format($rst['TotalCharges'],2) ?></td>
</tr>
<tr>
<td  align="right"><b>VAT </b>&nbsp;:</td>
<td style="border-bottom: 2px #000 solid" align="right"><?php echo number_format($vt_total_charges,2) ?></td>
</tr>
<tr>
<td align="right"><b>Gross Amount </b>&nbsp;:</td>
<td align="right" style="border-bottom: 5px #000 double"><b>P&nbsp;<?php echo number_format(($gt_total_charges+$nvt_charge_amount),2) ?></b></td>
</tr>
</table>
<?php }
}
?>

</td></tr><?php //end VAT and Total ?>
</table>
<!----- Billing //--->
<br />
<br />
<br />
<!-- <br />
<br /> -->
<!----- Approval //style="margin-top: 10px;"--->
<table border="0" cellpadding="0" cellspacing="0" width="85%" style="margin-top: -20px;">
<?php if($InvInfo['checkedby'] > 0) { ?>
<tr>
<td width="250"></td>
<td width="250">Checked By:</td>
<td width="250"></td>
</tr>
<?php } ?>
<tr>
<td width="250"><?php echo (file_exists('img/usersignature/'.$InvInfo['encodedby'].'.png')) ? '<img src="img/usersignature/'.$InvInfo['encodedby'].'.png" style="max-height:40px;" border=0>' : '' ?></td>
<td width="250"><?php echo (file_exists('img/usersignature/'.$InvInfo['checkedby'].'.png')) ? '<img src="img/usersignature/'.$InvInfo['checkedby'].'.png" style="max-height:40px;" border=0>' : '' ?></td>
<td width="250"><?php echo (file_exists('img/usersignature/'.$InvInfo['approvedby'].'.png')) ? '<img src="img/usersignature/'.$InvInfo['approvedby'].'.png" style="max-height:40px;" border=0>' : '' ?></td>
</tr>
<?php if($account=='SPI' || $account=='MNC' || $account=='HPI') { ?>
</br>
</br>
</br>
<?php } ?>

<tr>
<td width="250"><?php echo (trim($InvInfo['encodedbyName'])!='') ? '<span style="padding: 0 20px 0 20px; border-top:1px solid #000">'.$InvInfo['encodedbyName'].'</span>' : '' ?></td>
<td width="250"><?php echo (trim($InvInfo['checkedbyName'])!='') ? '<span style="padding: 0 20px 0 20px; border-top:1px solid #000">'.$InvInfo['checkedbyName'].'</span>' : '' ?></td>
<td width="250"><?php echo (trim($InvInfo['approvedbyName'])!='') ? '<span style="padding: 0 20px 0 20px; border-top:1px solid #000">'.$InvInfo['approvedbyName'].'</span>' : '' ?></td>
</tr>
</table>
<br>
<br>
<!--- Approval//--->
</div>
<?php if(IsClient()) { ?>
<br>
<br>
<?php } ?>
  </body>
</html>