<?php
if((IsClientAdmin() && !ValidateUserPermission(Admin_GroupID_Gatepass_prealerttab)) || (IsAdmin() && !ValidateUserPermission(All_GroupID_Gatepass_prealerttab))) {
	echo '<div class="alert alert-danger" align="center">
        <strong> Your account is not authorize to view Pre-alert tab.</strong></div>';
        exit;
}

$querytfship=doQuery("SELECT GROUP_CONCAT(DISTINCT(tfs.billcode)) as billcode_list
						FROM tf_shipments tfs 
						LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic 
						WHERE 
							(TRIM(COALESCE(tfs.csmno,''))='') 
							AND TRIM(tfs.waybillno)<>'' 
							AND wtfs.shipment_movement_status<>9
							AND COALESCE(wtfs.parent_waybill,0)=0");
$result=mysql_fetch_array($querytfship);
$billrows=explode(',',$result['billcode_list']);
$billrows_list="'" . implode ( "', '", $billrows ) . "'";
$qmodeshpmnt=doQuery("SELECT GROUP_CONCAT(DISTINCT(UOM_travel_desc)) as shipment_mode FROM web_mode_shipments WHERE Code IN (".$billrows_list.") ");
$shpresult=mysql_fetch_array($qmodeshpmnt);
$mode_desc=explode(',',$shpresult['shipment_mode']);
?>
<div class="container-fluid" id="pending_alert_container" style="display: none;">
	<div class="text-center" style="border-radius: 5px; padding: 10px 10px; font-size:18px; font-weight: bold; background: #ffff00; border: 1px solid #000;">
		The chosen driver or courier already has a DR or gatepass; a new gatepass cannot be created. To add a new CSM, choose the Dr/gatepass.
	</div>
	<br>
</div>
<script type="text/javascript">
	$(document).ready(function() {
		$('#tbl_waybills').DataTable();
		$('#tbl_scannedwb').DataTable();
	});
	function addCSMforCSM(table_row,button) {
		var tbl_waybills = $('#tbl_waybills').DataTable();
		var tbl_scannedwb = $('#tbl_scannedwb').DataTable();
		var removebtn ='<div class="text-center"><button class="btn btn-danger btn-small" title="Remove to list from Scanned CSM" onclick="RemoveCSMfromCSM($(this).closest(\'tr\'),0)">Remove</button></div>';
		var csmno=tbl_waybills.row(table_row).data()[0];
		var csmdate=tbl_waybills.row(table_row).data()[1];
		var gateway=tbl_waybills.row(table_row).data()[2];
		var sp=tbl_waybills.row(table_row).data()[3];
		var addRow = tbl_waybills.row(table_row);
	    tbl_scannedwb.row.add([csmno,csmdate,gateway,sp,removebtn]).draw();
	    addRow.remove().draw();
	}
		
		function RemoveCSMfromCSMupdate(table_row,button) {
			var drno='<?php echo $_GET['drno'] ?>';
			var tbl_waybills = $('#tbl_waybills').DataTable();
			var tbl_scannedwb = $('#tbl_scannedwb').DataTable();
			var addbtn ='<div class="text-center"><button class="btn btn-primary btn-small" title="Add to list for CSM" onclick="addCSMforCSM($(this).closest(\'tr\'),0)">Add</button></div>';
			var csmnorow=tbl_scannedwb.row(table_row).data()[0];
			var csmdate=tbl_scannedwb.row(table_row).data()[1];
			var gateway=tbl_scannedwb.row(table_row).data()[2];
			var sp=tbl_scannedwb.row(table_row).data()[3];
          
			$('#waybillremove').html(drno);
			$('#modal_remove').modal();
			
			$("#remove_okay_btn").on("click", function (e){
				e.preventDefault();
				$("#modal_remove .close").click();
				var csm_info=$('#csm_info  option:selected').val();
			    var other_info=$('#other_info').val();
				$.ajax({
		            type: "POST",
		            url: "csm_create_process.php?donotshowheader=1",
		            data: {process:'consolidate_update',csmno:csmno,waybill:waybill,csm_info:csm_info,other_info:other_info},
		            success: function(data) {
						response = JSON.parse(data);
						if(response.success===true){
							tbl_scannedwb.row(table_row).data()[5] = '<div class="text-center"><button class="btn btn-primary btn-small" title="Add to list for CSM" onclick="addCSMforCSM($(this).closest(\'tr\'),0)">Add</button></div>';
							tbl_waybills.row.add(tbl_scannedwb.row(table_row).data()).draw();
							tbl_scannedwb.row(table_row).remove().draw();
						}
					}
		        });
			});
		}
		
		function RemoveCSMfromCSM(table_row,button) {
			var tbl_waybills = $('#tbl_waybills').DataTable();
			var tbl_scannedwb = $('#tbl_scannedwb').DataTable();
			var addbtn ='<div class="text-center"><button class="btn btn-primary btn-small" title="Add to list for CSM" onclick="addCSMforCSM($(this).closest(\'tr\'))">Add</button></div>';
			var csmno=tbl_scannedwb.row(table_row).data()[0];
			var csmdate=tbl_scannedwb.row(table_row).data()[1];
			var gateway=tbl_scannedwb.row(table_row).data()[2];
			var sp=tbl_scannedwb.row(table_row).data()[3];
			var addRow = tbl_scannedwb.row(table_row);
		    tbl_waybills.row.add([csmno,csmdate,gateway,sp,addbtn]).draw();
		    addRow.remove().draw();
			tbl_scannedwb.search("").draw();
		}

</script>
<?php if(isset($_GET['edit']) && $_GET['edit']==1 && isset($_GET['drno']) && $_GET['drno']<>'') { ?>
<div class="container-fluid" style="border-radius: 5px; padding: 10px 10px; font-size:18px; font-weight: bold; background: #ffff00; border: 1px solid #000;">
Change DR# <?php echo $_GET['drno'] ?>
</div>
<br>
<?php
}

/*$qfilterdata=doQuery("SELECT gateway,spname,transmode,city_limit FROM tf_consolidate WHERE csmno='".$_GET['csmno']."' ");
$resultfilter=mysql_fetch_array($qfilterdata);

$spdata=doQuery("SELECT mfp.spgateway_id,mfp.sp_id,sz.subarea
				 FROM mf_partners mfp
				 LEFT JOIN sf_zipcode sz ON sz.gateway=mfp.spgateway
				 WHERE mfp.spname='".$resultfilter['spname']."' AND mfp.spgateway='".$resultfilter['gateway']."' ");
$rowsp=mysql_fetch_array($spdata);
$filtersp=$rowsp['sp_id'].','.$rowsp['spgateway_id'].','.$rowsp['subarea'];*/

if($_GET['consolidator']<>'' || $_GET['drtype']=='DOMESTIC LINE HAUL TRANSFER'){
	$displaycons='';
} else {
	$displaycons='style="display: none;"';
}
if($_GET['courier']<>''){
	$displaycor='';
} else {
	$displaycor='style="display: none;"';
}
if($_GET['spcourier']<>''){
	$displaysp='';
} else {
	$displaysp='style="display: none;"';
}
if($_GET['plateno']<>'' && $_GET['couriertype']==1){
	$displayplate='';
} else {
	$displayplate='style="display: none;"';
}
if($_GET['pickupdriver']<>''){
	$displaypickupdriver='';
} else {
	$displaypickupdriver='style="display: none;"';
}
if($_GET['pickupplate']<>'' && $_GET['couriertype']==1){
	$displaypickupplate='';
} else {
	$displaypickupplate='style="display: none;"';
}
?>
<div class="container-fluid">
	<div class="row">
		<!-- <div class="col-sm-2">
			 <div class="form-group">
			 	<label>Budget Required</label>
				<select class="form-control" name="IsBudgetRequired" id="IsBudgetRequired" required>
					<option value="1">YES</option>
					<option value="0">NO</option>
				</select>
			</div>
		</div> -->
		<div class="col-sm-2">
			<div class="form-group">
				<label>Scheduled Dispatch Date & Time</label>
                <div class='input-group date' id='drdate'>
                    <input type='text' class="form-control" name="date" required="" id="scheduled_date" 
                    value="<?php echo (isset($_GET['scheduled_dispatch_date']) && $_GET['scheduled_dispatch_date']<>'' ? $_GET['scheduled_dispatch_date'] : date("Y-m-d H:i:s")) ?>" />
                    <span class="input-group-addon">
                        <span class="glyphicon glyphicon-calendar"></span>
                    </span>
                </div>
			</div>
		</div>
		<script type="text/javascript">
            $(function () {
                 $('#drdate').datetimepicker({
	                format: 'YYYY-MM-DD HH:mm:ss',
					useCurrent: 'minute' 
	           });
            }); 
        </script>
		<div class="col-sm-2">
			 <div class="form-group">
			 	<label>Dispatch Type</label>
				<select class="form-control selectpicker" name="drtype" id="drtype" onchange="get_waybills();" required>
					<option value="">Type of Dispatch</option>
					<?php
					$result=doQuery("SELECT DISTINCT value1 FROM sf_codes WHERE ctype='DISPATCH' GROUP BY value1");
					while($wmodes=mysql_fetch_array($result)){
						$selected='';
						if($_GET['drtype']<>'' && $_GET['drtype']==$wmodes['value1']){
							$selected='selected';
						}
					?>
					<option value="<?php echo $wmodes['value1'] ?>" <?php echo $selected?>><?php echo $wmodes['value1'] ?></option>
					<?php }  ?>
				</select>
			</div>
		</div>
		<div class='col-sm-2' id="linehaul" <?php echo $displaycons ?>>
            <div class="form-group">
            	<label>Consolidator</label>
                <select class="form-control selectpicker" name="linehaul" id="consolidator" onchange="get_waybills()">
                	<option value="">Select Consolidator</option>
					<?php
					$result=doQuery("SELECT DISTINCT value1 FROM sf_codes WHERE ctype='LINEHAUL' GROUP BY value1");
					while($rmodes=mysql_fetch_array($result)){
						$selected='';
						if($_GET['consolidator']<>'' && $_GET['consolidator']==$rmodes['value1']){
							$selected='selected';
						}
					?>
					<option value="<?php echo $rmodes['value1'] ?>" <?php echo $selected ?>><?php echo $rmodes['value1'] ?></option>
					<?php } ?>
				</select>
            </div>
        </div>
        <div class="col-sm-2">
			<div class="form-group">
				<label>Courier Type</label>
				<select class="form-control selectpicker" name="couriertype" id="couriertype" onchange="couriertypes();">
					<option value="">Select Courier Type</option>
					<option value="1" <?php echo ($_GET['couriertype']<>'' && $_GET['couriertype']==1 ? 'selected' : '') ?>>IN-HOUSE COURIER</option>
					<option value="2" <?php echo ($_GET['couriertype']<>'' && $_GET['couriertype']==2 ? 'selected' : '') ?>>SERVICE PROVIDER COURIER</option>
					<option value="3" <?php echo ($_GET['couriertype']<>'' && $_GET['couriertype']==3 ? 'selected' : '') ?>>PICK UP FROM WAREHOUSE</option>
				</select>
			</div>
		</div>
		<div class="col-sm-2" <?php echo $displaypickupdriver ?> id="pickup_driver">
			<div class="form-group">
				<label>Pickup Driver</label>
				<input type="text" name="" value="<?php echo $_GET['pickupdriver'] ?>" class="form-control" placeholder="Driver Name" id="pickupdriver">
			</div>
		</div>
		<div class="col-sm-2" <?php echo $displaypickupplate ?> id="pickup_plateno">
			<div class="form-group">
				<label>Pickup Plate#</label>
				<input type="text" name="" class="form-control" value="<?php echo $_GET['pickupplate'] ?>" placeholder="Plate #" id="pickupplate">
			</div>
		</div>
		<div class="col-sm-2" <?php echo $displaycor ?> id="row_inhouse">
			<div class="form-group" >
				<label>Driver</label>
				<select class="form-control selectpicker" name="courier" id="courier" onchange="filter_DR(this.val)">
					<option value="">Select Courier</option>
					<?php
					$result=doQuery("SELECT DISTINCT wp.sp_id,sf.value1 FROM sf_codes sf
									 LEFT JOIN web_webusers ww ON ww.sp_id=sf.sp_id
								     LEFT JOIN web_mf_partners wp ON wp.sp_id=sf.sp_id
									 WHERE (ww.sp_id IS NOT NULL) AND (sf.ctype='COURIER') AND (wp.sp_user_type=2) AND (sf.status=1) AND (wp.sp_status=0) AND (sf.sp_id > 0) ORDER BY sf.value1 ");
					while($rmodes=mysql_fetch_array($result)){
						$selected='';
						if($_GET['courier']<>'' && $_GET['courier']==$rmodes['sp_id']){
							$selected='selected';
						}
					?>
					<option value="<?php echo $rmodes['sp_id'] ?>" <?php echo $selected ?>><?php echo $rmodes['value1'] ?></option>
					<?php } ?>
				</select>
				
			</div>
		</div>
		<div class="col-sm-2" id="row_pending_dr" style="display: none;">
			<div class="form-group">
				<label>Pending DR#</label>
				<select class="form-control selectpicker" name="pending_dr" id="pending_dr" data-live-search="true" onchange="pendingDRSelected()">
				</select>
			</div>
		</div>
		<div class="col-sm-2" <?php echo $displaycor ?> id="row_courier_helper">
			<div class="form-group">
				<label>Driver/Helper</label>
				<select class="form-control selectpicker" name="courier_helper" id="courier_helper">
					<option value="">Select Courier Helper</option>
					<?php
					$result=doQuery("SELECT DISTINCT sf.value1,wp.sp_id FROM sf_codes sf
									 LEFT JOIN web_webusers ww ON ww.sp_id=sf.sp_id
								     LEFT JOIN web_mf_partners wp ON wp.sp_id=sf.sp_id
									 WHERE (ww.sp_id IS NOT NULL) AND (sf.ctype='COURIER') AND (wp.sp_user_type=2) AND (sf.status=1) AND (wp.sp_status=0) AND (sf.sp_id > 0) ORDER BY sf.value1 ");
					while($rmodes=mysql_fetch_array($result)){
						$selected='';
						if($_GET['courier_helper']<>'' && $_GET['courier_helper']==$rmodes['sp_id']){
							$selected='selected';
						}
					?>
					<option value="<?php echo $rmodes['sp_id'] ?>" <?php echo $selected ?>><?php echo $rmodes['value1'] ?></option>
					<?php } ?>
				</select>
				
			</div>
		</div>
		<div class="col-sm-2" <?php echo $displaycor ?> id="row_courier_helper2">
			<div class="form-group">
				<label>Helper</label>
				<select class="form-control selectpicker" name="courier_helper2" id="courier_helper2">
					<option value="">Select Courier Helper</option>
					<?php
					$result=doQuery("SELECT DISTINCT sf.value1,wp.sp_id FROM sf_codes sf
									 LEFT JOIN web_webusers ww ON ww.sp_id=sf.sp_id
								     LEFT JOIN web_mf_partners wp ON wp.sp_id=sf.sp_id
									 WHERE (ww.sp_id IS NOT NULL) AND (sf.ctype='COURIER') AND (wp.sp_user_type=2) AND (sf.status=1) AND (wp.sp_status=0) AND (sf.sp_id > 0) ORDER BY sf.value1 ");
					while($rmodes=mysql_fetch_array($result)){
						$selected='';
						if($_GET['courier_helper2']<>'' && $_GET['courier_helper2']==$rmodes['sp_id']){
							$selected='selected';
						}
					?>
					<option value="<?php echo $rmodes['sp_id'] ?>" <?php echo $selected ?>><?php echo $rmodes['value1'] ?></option>
					<?php } ?>
				</select>
			</div>
		</div>
		<div class="col-sm-2" <?php echo $displaysp ?> id="row_sp">
			<div class="form-group">
				<label>Service Provider / Contractor Courier</label>
				<select class="form-control selectpicker" name="spcourier" id="spcourier">
					<option value="">Select Service Provider / Contractor Courier</option>
					<?php
					$result=doQuery("SELECT * FROM web_mf_partners WHERE (sfc_id>0) AND (sp_user_type=3) ORDER BY sp_name");
					while($rmodes=mysql_fetch_array($result)){
						$selected='';
						if($_GET['spcourier']<>'' && $_GET['spcourier']==$rmodes['sp_id']){
							$selected='selected';
						}
					?>
					<option value="<?php echo $rmodes['sfc_id'] ?>" <?php echo $selected ?>><?php echo $rmodes['sp_name'] ?></option>
					<?php } ?>
				</select>
			</div>
		</div>
		<div class="col-sm-2" <?php //echo $displaysp ?> id="vehicle_type_div">
			<div class="form-group">
				<label>Vehicle Type</label>
				<select name="" id="vehicle_type" class="form-control">
					<option value=""></option>
					<?php 
					$qsubconrate=doQuery("SELECT DISTINCT vehicle_type FROM web_mode_subcon_truck_rates WHERE status=1 AND effectivity_date >= '2025-04-01'");
					while($rowsubconrate=mysql_fetch_array($qsubconrate)){
					?>
					<option value="<?php echo $rowsubconrate['vehicle_type'] ?>"><?php echo $rowsubconrate['vehicle_type'] ?><option>
					<?php } ?>
				</select>
			</div>
		</div>
		<div class='col-sm-2' id="row_plate" <?php echo $displayplate ?>>
            <div class="form-group">
            	<label>Plate#</label>
				<select class="form-control" name="plateno" id="plateno" required="">
					<option value="">Select Plate #</option>
					<?php
					/*$cons=doQuery("SELECT GROUP_CONCAT(DISTINCT sp_id) as sp_id_list FROM tf_consolidate WHERE csmno IN (".$_REQUEST['csmno'].") ");
					$list=mysql_fetch_array($cons);
					$explodelist=explode(',', $list['sp_id_list']);
					$spid_list="'" . implode ( "', '", $explodelist ) . "'";*/
					if(isset($_GET['couriertype']) && $_GET['couriertype']==1){
						$qwebtruck=doQuery("SELECT * FROM web_sp_truck WHERE tsprovider='LWLI' ");
					} else {
						$qwebtruck=doQuery("SELECT * FROM web_sp_truck WHERE sp_id='".$_GET['spcourier']."' ");
					}
					if(mysql_num_rows($qwebtruck) <=0 ){
						echo '<option value=""> No available service vehicle </option>';
					} else {
						while($rowlist=mysql_fetch_array($qwebtruck)){
							$selected='';
							if($_GET['plateno']<>'' && $_GET['plateno']==$rowlist['plateno']){
								$selected='selected';
							}
							echo '<option value="'.$rowlist['plateno'].'" '.$selected.'> '.$rowlist['plateno'].' / '.$rowlist['tsprovider'].' </option>';
						}
					}
					?>
				</select>
            </div>
        </div>
		<!-- <div class="col-sm-2">
			<div class="form-group">
			<label>&nbsp;</label>
			<button type="button" class="btn btn-primary btn-sm form-control" onclick="get_waybills();"><i class="icon-search"></i> GET CSM </button>
			</div>
		</div> -->
	</div>
	 <hr>
	<div class="row">
		<div class="col-sm-6">
			<table class="table-bordered table" id="tbl_waybills">
				<caption id="VISAYAS-CAPTION-ALL">
				<button type="button" class="btn btn-primary btn-small"<?php echo (isset($_GET['csmno']) && trim($_GET['csmno'])!='' && isset($_GET['edit']) && $_GET['edit']==1) ? ' disabled ' : '' ?>id="ScanCSMBtn">Scan CSM</button>
				</caption>
				<thead>
					<tr>
						<th colspan="6" align="center">
							CSM FOR DISPATCH
						</th>
					</tr>
					<tr>
						<th>CSM#</th>
						<th>CSM Date</th>
						<?php
						if(isset($_GET['drtype']) && $_GET['drtype']=='DIRECT DELIVERY TO CONSIGNEE'){
						?>
						<th>Area</th>
						<th>Cluster</th>
						<?php } else { ?>
						<th>Gateway</th>
						<th>Service Partner</th>
						<?php } ?>
						<th>Action</th>
					</tr>
				</thead>
				<tbody>
					<?php
		             	if(isset($_GET['drtype']) && trim($_GET['drtype'])<>'') {
		             	$condition='';
						if(isset($_GET['drtype']) && $_GET['drtype']=='DIRECT DELIVERY TO CONSIGNEE'){
							$condition='AND tc.gateway="DIRECT"'; 
						} elseif(isset($_GET['drtype']) && $_GET['drtype']=='DIRECT DELIVERY TO CONSIGNEE (PICKUP AND DELIVER)'){
							$condition='AND tc.gateway="DIRECT" AND tc.pickupanddeliver=1';
						} elseif(isset($_GET['drtype']) && $_GET['drtype']=='DIRECT DELIVERY TO SP'){
							$condition='AND tc.gateway<>"DIRECT" AND COALESCE(tc.consolidator,"")=""';
						} elseif(isset($_GET['drtype']) && $_GET['drtype']=='DOMESTIC LINE HAUL TRANSFER') {
							$condition='AND tc.gateway<>"DIRECT"';
						} else {
							$condition='AND tc.gateway!="DIRECT"';
						}
						$filter_transmode='';
						if(isset($_GET['consolidator']) && $_GET['consolidator']<>''){
							$qlinehaul=doQuery("SELECT transmode FROM sf_codes WHERE ctype='LINEHAUL' AND value1='".$_GET['consolidator']."' ");
							$rowshipment=mysql_fetch_array($qlinehaul);
							$transmode=$rowshipment['transmode'];
							if(trim($transmode)!='') {
							  $condition.=' AND (tc.transmode="'.$transmode.'" OR tc.transmode="") ';
							}
						}
						$filtercsm='AND tc.PackingDone=1';
						if(IsDirectDeliveryOnlyWithoutScanningSupport()){
							$filtercsm='AND pickupanddeliver=1';
						}

						$query=doQuery("SELECT DISTINCT tc.csmno,
											   tc.csmdate,
											   tc.gateway,
											   tc.spname,
											   tc.area
									FROM 
										tf_consolidate tc
										LEFT JOIN tf_shipments tfs ON (tfs.csmno=tc.csmno OR tfs.csmno IS NULL)
										LEFT JOIN web_tf_shipments wtfs ON wtfs.shipment_magic=tfs.billed AND (NOT wtfs.shipment_movement_status IN ('".DELIVERED_STATUS_ID."','".CANCELLED_STATUS_ID."'))
									WHERE
										tc.if_child!=1 AND
										(wtfs.shipment_magic IS NOT NULL) AND
										tc.status_index<>".CANCELLED_STATUS_ID." AND
										tc.status<>'".CANCELLED_STATUS."' AND
										TRIM(COALESCE(tc.drno,''))='' 
										AND tc.csmdate>'2021-06-30 00:00:00'
										AND (COALESCE(tc.csmno,'')<>'')
										 ".$filtercsm." ".$condition." ".QueryFilterByDepartment('departmentID','wtfs','AND')." ");
					
					while($result=mysql_fetch_array($query)){
						if(trim($result['area'])=='' && $_GET['drtype']=='DIRECT DELIVERY TO CONSIGNEE'){
							$getarea=doQuery("SELECT GROUP_CONCAT(DISTINCT sfz.subarea) as subarea,
													 GROUP_CONCAT(DISTINCT sfz.ssubarea) as ssubarea
													 FROM tf_shipments tfs
													 LEFT JOIN sf_zipcode sfz
											         ON tfs.zipcode=sfz.zipcode
												     WHERE tfs.csmno='".$result['csmno']."'");
							$rowgetarea=mysql_fetch_array($getarea);

							$subarea=$rowgetarea['subarea'];
							$cluster=$rowgetarea['ssubarea'];
						} else {
							$area=explode('|',$result['area']);
							$subarea=$area[0];
							$cluster=$area[1];
						}
					?>
					<tr>
						<td><a href="javascript:ShowTransDetails('gatepass/csm_list.php?csmno=<?php echo $result['csmno'] ?>&donotshowheader=1','CSM INFO')"><?php echo $result['csmno'] ?></a></td>
						<td><?php echo date('F d, Y',strtotime($result['csmdate'])) ?></td>
						<?php
						if(isset($_GET['drtype']) && $_GET['drtype']=='DIRECT DELIVERY TO CONSIGNEE'){
						?>
						<td><?php echo $subarea ?></td>
						<td><?php echo $cluster ?></td>
						<?php } else { ?>
						<td><?php echo $result['gateway'] ?></td>
						<td><?php echo $result['spname'] ?></td>
						<?php } ?>
		                <td>
		                	<div class="text-center"><button class="btn btn-primary btn-small" title="Add to list for CSM" onclick="addCSMforCSM($(this).closest('tr'))">Add</button></div>
		                </td>
					</tr>

					<?php }
					}
					?>
				</tbody>
			</table>
		</div>
		<div class="col-sm-6">
			<table class="table-bordered table" id="tbl_scannedwb">
				<caption id="VISAYAS-CAPTION-ALL" class="text-right">
				<button type="button" id="saveCSM" class="btn btn-success btn-small" onclick="saveCSM();">Save Dispatch</button></caption>
				<thead>
					<tr>
						<th colspan="6" align="center">
							SCANNED/SELECTED CSM FOR DISPATCH
						</th>	
					</tr>
					<tr>
						<th>CSM#</th>
						<th>CSM Date</th>
						<?php 
						if(isset($_GET['drtype']) && $_GET['drtype']=='DIRECT DELIVERY TO CONSIGNEE'){
						?>
						<th>Area</th>
						<th>Cluster</th>
						<?php } else { ?>
						<th>Gateway</th>
						<th>Service Partner</th>
						<?php } ?>
						<th>Action</th>
					</tr>
				</thead>
				<tbody>
					<?php
					if(isset($_GET['edit']) && isset($_GET['drno'])){
						$qshipment=doQuery("SELECT csmno,csmdate,gateway,spname,area
										    FROM tf_consolidate
										    WHERE drno='".$_GET['drno']."' ");
						while($data=mysql_fetch_array($qshipment)){
							if(trim($data['area'])=='' && $_GET['drtype']=='DIRECT DELIVERY TO CONSIGNEE'){
								$getarea=doQuery("SELECT GROUP_CONCAT(DISTINCT sfz.subarea) as subarea,
														 GROUP_CONCAT(DISTINCT sfz.ssubarea) as ssubarea
														 FROM tf_shipments tfs
														 LEFT JOIN sf_zipcode sfz ON tfs.zipcode=sfz.zipcode
													     WHERE tfs.csmno='".$data['csmno']."'");
								$rowgetarea=mysql_fetch_array($getarea);

								$subarea=$rowgetarea['subarea'];
								$cluster=$rowgetarea['ssubarea'];
							} else {
								$area=explode('|',$data['area']);
								$subarea=$area[0];
								$cluster=$area[1];
							}
					?>
					<tr>
						<td><a href="javascript:ShowTransDetails('gatepass/csm_list.php?csmno=<?php echo $data['csmno'] ?>&donotshowheader=1','CSM INFO') "><?php echo $data['csmno'] ?></a></td>
						<td><?php echo date('F d, Y',strtotime($data['csmdate'])) ?></td>
						<?php
						if(isset($_GET['drtype']) && $_GET['drtype']=='DIRECT DELIVERY TO CONSIGNEE'){
						?>
						<td><?php echo $subarea ?></td>
						<td><?php echo $cluster ?></td>
						<?php } else { ?>
						<td><?php echo $data['gateway'] ?></td>
						<td><?php echo $data['spname'] ?></td>
						<?php } ?>
						<td>
							<div class="text-center"><button class="btn btn-danger btn-small" title="Remove to list from Scanned CSM" onclick="RemoveCSMfromCSM($(this).closest('tr'))">Remove</button></div>
						</td>
					</tr>
					<?php
					}
					}
					?>
				</tbody>
			</table>
		</div>
	</div>
  <br>
  <hr>
  
   <!-- Modal -->
	<div id="scanCSM" class="modal fade" role="dialog">
	  <div class="modal-dialog">
		<!-- Modal content-->
		<div class="modal-content">
		  <div class="modal-header">
			<button type="button" class="close" data-dismiss="modal">&times;</button>
			<h4 class="modal-title">Scan CSM Barcode</h4>
		  </div>
		  <div class="modal-body">
			<input type="text" name="" id="wb_barcode" placeholder="CSM Number" class="form-control">
			<div class="radio">
			  <label><input type="radio" name="optradio" checked id="radio1">Add to list for dispatch</label>
			  <label><input type="radio" name="optradio" id="radio2">Remove from list of scanned/selected </label>
			</div>
		  </div>
		  <div class="modal-footer">
			<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
		  </div>
		</div>
	  </div>
	</div>
	
  <!-- Modal -->
	<div id="modal_remove" class="modal fade" role="dialog">
	  <div class="modal-dialog">
	    <!-- Modal content-->
	    <div class="modal-content">
	      <div class="modal-header">
	        <button type="button" class="close" data-dismiss="modal">&times;</button>
	        <h4 class="modal-title">Remove DR#<span id="waybillremove"></span></h4>
	      </div>
		  <div class="modal-body">
	        <label>Reason:</label>
	      	<select class="form-control" name="csm_info" id="csm_info">
	      		<?php
	      		$qinfo=doQuery("SELECT * FROM sf_codes WHERE ctype='CSM_INFO' ORDER BY value1");
	      		while($rowinfo=mysql_fetch_array($qinfo)){
	      		?>
 	      		<option value="<?php echo $rowinfo['value1'] ?>"><?php echo $rowinfo['value1'] ?></option>
 	      		<?php
 	      		}
 	      		?>
	      	</select>
	      	<label>Others:</label>
	      	<input type="text" name="other_info" id="other_info" value="" class="form-control">
	      </div>
	      <div class="modal-footer">
	      	<button type="button" class="btn btn-success" id="remove_okay_btn">OK</button>
	        <button type="button" class="btn btn-default" data-dismiss="modal" id="close_btn">Close</button>
	      </div>
	    </div>
	  </div>
	
	

<script type="text/javascript">
	function pendingDRSelected(){
		var dr = $('#pending_dr').val();
		if(!dr){ return; }
		$.ajax({
			type: "POST",
			url: "process_delivery_dispatch.php?donotshowheader=1",
			data: {process:'get_dr_details', drno: dr},
			success: function(data){
				try{
					var resp = JSON.parse(data);
					if(resp.success===true){
						var drtype = (resp.drtype==='DIRECT DELIVERY') ? 'DIRECT DELIVERY TO CONSIGNEE' : resp.drtype;
						var couriertype = (resp.courier_type && resp.courier_type!=='') ? resp.courier_type : 1;
						var url = 'delivery_dispatch.php?showtab=0&drno='+encodeURIComponent(resp.drno)+'&edit=1'
							+ '&drdate='+encodeURIComponent(resp.drdate)
							+ '&drtype='+encodeURIComponent(drtype)
							+ '&consolidator='+encodeURIComponent(resp.consolidator || '')
							+ '&couriertype='+encodeURIComponent(couriertype)
							+ '&courier='+encodeURIComponent(resp.sfc_id || '')
							+ '&courier_helper='+encodeURIComponent(resp.sfc_helper_id || '')
							+ '&courier_helper2='+encodeURIComponent(resp.sfc_helper2_id || '')
							+ '&spcourier='+encodeURIComponent(resp.spcourier || '')
							+ '&plateno='+encodeURIComponent(resp.plateno || '')
							+ '&pickupdriver='+encodeURIComponent(resp.pickup_driver || '')
							+ '&pickupplate='+encodeURIComponent(resp.pickup_plateno || '');
						window.location.href = url;
					}
				} catch(e){
					console.log('Invalid response', e, data);
				}
			}
		});
	}

	function filter_DR(){
		var courier_id = $('#courier').val();
		if(!courier_id){
			$('#row_pending_dr').hide();
			$('#pending_dr').empty();
			$('#pending_dr').selectpicker && $('#pending_dr').selectpicker('refresh');
			$('#pending_alert_container').hide();
			return;
		}
		$.ajax({
			type: "POST",
			url: "process_delivery_dispatch.php?donotshowheader=1",
			data: {process:'get_pending_dr', sfc_id: courier_id},
			success: function(data){
				try{
					var response = JSON.parse(data);
					if(response.success===true){
						$('#pending_dr').empty();
						if(response.has_pending){
							for(var i=0;i<response.dr_list.length;i++){
								$('#pending_dr').append(new Option(response.dr_list[i], response.dr_list[i]));
							}
							$('#row_pending_dr').show();
							$('#pending_alert_container').show();
						} else {
							$('#row_pending_dr').hide();
							$('#pending_alert_container').hide();
						}
						$('#pending_dr').selectpicker && $('#pending_dr').selectpicker('refresh');
					}
				} catch(e){
					console.log('Invalid response', e, data);
				}
			},
			error: function(){
				$('#row_pending_dr').hide();
				$('#pending_dr').empty();
				$('#pending_dr').selectpicker && $('#pending_dr').selectpicker('refresh');
				$('#pending_alert_container').hide();
			}
		});
	}
	
	function ShowTransDetails(eurl,etitle) {
         eModal.iframe(eurl,etitle);
    }

	$("#ScanCSMBtn").click(function() {
	  $('#scanCSM').modal();
	});
	
	$( "#wb_barcode" ).keypress(function( event ) {
		if ( event.which == 13 ) {
			var csmno=$("#wb_barcode").val();
			var tbl_waybills = $('#tbl_waybills').DataTable();
			var tbl_scannedwb = $('#tbl_scannedwb').DataTable();

			if ($("#radio1").is(':checked')) {//transfer to scanned waybills
				var currentrow=tbl_waybills.rows().count();
				var count=1;
				tbl_waybills.rows().every( function ( rowIdx, tableLoop, rowLoop ) {
					var removebtn ='<div class="text-center"><button class="btn btn-danger btn-small" title="Remove to list from Scanned CSM" onclick="RemoveCSMfromCSM($(this).closest(\'tr\'))">Remove</button></div>';
					var data = this.data();
					if(this.data()!=null) {
						var csmnodata=this.data()[0];
						var csmdate=this.data()[1];
						var gateway=this.data()[2];
						var sp=this.data()[3];
						if(csmnodata.indexOf(csmno)>=0){
							tbl_scannedwb.row.add([csmnodata,csmdate,gateway,sp,removebtn]).draw();
							tbl_waybills.rows(rowIdx).remove().draw();
							$("#wb_barcode").val('');
							return false;
						} else {
							if(count==currentrow){
								$.toast({
									heading: 'Info!',
									text: 'Invalid csm no. '+csmno,
									showHideTransition: 'slide',
									icon: 'error',
									position: 'mid-center'
								});
								$("#wb_barcode").val('');
								return false;
							}
						}
					}
					count++;
				});
			} else {//remove from scanned waybills
				var currentrow=tbl_scannedwb.rows().count();
				var count=1;
				tbl_scannedwb.rows().every( function ( rowIdx, tableLoop, rowLoop ) {
					var addbtn ='<div class="text-center"><button class="btn btn-primary btn-small" title="Add to list for CSM" onclick="addCSMforCSM($(this).closest(\'tr\'))">Add</button></div>';
					var data = this.data();
					if(this.data()!=null) {
						var csmnodata=this.data()[0];
						var csmdate=this.data()[1];
						var gateway=this.data()[2];
						var sp=this.data()[3];
						if(csmnodata.indexOf(csmno)>=0){
							//transfer to scanned waybills
							tbl_waybills.row.add([csmnodata,csmdate,gateway,sp,addbtn]).draw();
							tbl_scannedwb.rows(rowIdx).remove().draw();
							$("#wb_barcode").val('');
							return false;
						} else {
							if(count==currentrow){
								$.toast({
									heading: 'Info!',
									text: 'Invalid csm no. '+wbno,
									showHideTransition: 'slide',
									icon: 'error',
									position: 'mid-center'
								});
								$("#wb_barcode").val('');
								return false;
							}
						}
						count++;
					}
				});
			}
		}
		
	});

	function saveCSM(){
		var table2 = $('#tbl_scannedwb').DataTable();
		var scheduled_date = $('#scheduled_date').val();
		var drtype = $('#drtype').val();
		var consolidator = $('#consolidator').val();
		var couriertype = $('#couriertype').val();
		var courier = $('#courier').val();
		var spcourier = $('#spcourier').val();
		var pickupdriver = $('#pickupdriver').val();
		var pickupplate = $('#pickupplate').val();
		var courier_helper = $('#courier_helper').val();
		var courier_helper2 = $('#courier_helper2').val();
		var plateno = $('#plateno').val();
		var vehicle_type = $('#vehicle_type').val();
		var edit='<?php echo $_GET['edit'] ?>';
		var drno='<?php echo $_GET['drno'] ?>';
		var createdby='<?php echo $_SESSION['userid'] ?>';
		//var IsBudgetRequired=$('#IsBudgetRequired').val();
		var array = [];
		
		$("#tbl_scannedwb tbody tr").each(function(i) {
			if(typeof(table2.row(this).data())!='undefined' ) {
			  array.push($(table2.row(this).data()[0]).text());
			}
		});
    
	 	function submitdata() {
			$("#saveCSM").attr('disabled','disabled');
			$("#saveCSM").text('Please wait...');
			$.ajax({
				type: "POST",
				url: "process_delivery_dispatch.php?donotshowheader=1",
				//parameter
				data: {process:'dispatch',array:array,edit:edit,drno:drno,scheduled_date:scheduled_date,drtype:drtype,consolidator:consolidator,couriertype:couriertype,courier:courier,courier_helper:courier_helper,courier_helper2:courier_helper2,spcourier:spcourier,plateno:plateno,pickupdriver:pickupdriver,pickupplate:pickupplate,createdby:createdby,vehicle_type:vehicle_type},
				success: function(data) {
					response = JSON.parse(data);
					if(response.success===true){
						$.toast({
							heading: 'Well Done!',
							text: response.status,
							showHideTransition: 'slide',
							icon: 'success',
							position: 'mid-center'
						});
						var origurl = '<?php echo Domain_Location ?>'+"/delivery_dispatch.php?showtab=2";
						//var str = str.replace("&edit=1","").replace("&drno=<?php //echo $_GET['drno'] ?>","");
						//window.setTimeout( function(){ location.href = origurl; },3000);

					} else {
						$.toast({
							heading: 'Error!',
							text: response.status,
							showHideTransition: 'slide',
							icon: 'error',
							position: 'mid-center'
						});
					}
					$("#saveCSM").text('Save Dispatch');
					$("#saveCSM").removeAttr('disabled');
				}
			});
		}
		submitdata();
	}

	function get_waybills() {
		drType();
		var scheduled_date= $('#scheduled_date').val();
		var drtype= $('#drtype').val(); 
		var consolidator= $('#consolidator').val();
		var couriertype= $('#couriertype').val();
		var courier= $('#courier').val();
		var courier_helper= $('#courier_helper').val();
		var courier_helper2= $('#courier_helper2').val();
		var spcourier= $('#spcourier').val();
		var plateno= $('#plateno').val();
		var pickupdriver = $('#pickupdriver').val();
		var pickupplate = $('#pickupplate').val();
		var vehicle_type = $('#vehicle_type').val();
		/*var couriertype='<?php echo $_GET['edit'] ?>';
		var csmno='<?php echo $_GET['csmno'] ?>';*/
			var origurl = '<?php echo Domain_Location ?>'+"/delivery_dispatch.php?showtab=0&RefreshList=1";
			/*if(edit!='' && csmno!=''){
				var url = origurl+"&edit="+edit+"&csmno="+csmno+"&spgateway="+spgateway+"&city_limit="+city_limit+"&shpmode="+billcode;
			} else{*/
				var url = origurl+"&scheduled_dispatch_date="+scheduled_date+"&drtype="+drtype+"&consolidator="+consolidator+"&couriertype="+couriertype+"&courier="+courier+"&courier_helper="+courier_helper+"&courier_helper2="+courier_helper2+"&spcourier="+spcourier+"&plateno="+plateno+"&pickupdriver="+pickupdriver+"&pickupplate="+pickupplate+"&vehicle_type="+vehicle_type;
			//}
			window.location.href = url;
	}

	function couriertypes(){ 
		var couriertype= $('#couriertype').val();
		if(couriertype==1){
			$('#row_inhouse').show();
			$('#row_courier_helper').show();
			$('#row_courier_helper2').show();
			$('#row_plate').show();
			$('#save_btn').show();
			$('#row_sp').hide();
			$('#pickup_driver').hide();
			$('#pickup_plateno').hide();
			$('#vehicle_type_div').hide();
			$.ajax({ 
		  		type:"POST", 
		  		url : "delivery_dispatch.php?donotshowheader=1",
		  		data: {
	        		get_plateno:'1',
	        		sp_id:'31'
	        	  },
	               success : function(data) {
	               response = JSON.parse(data);
	                $("#plateno").empty();
	               if(response.success===false){
	               } else{
	               		var vehicle_list=response.gtprovider;
						var i;
						for (i = 0; i < vehicle_list.length; i++) {
						  $("#plateno").append(new Option(vehicle_list[i]['plateno'] +' / '+ vehicle_list[i]['provider'],vehicle_list[i]['plateno'] ));
						  
						}
	               }
				},
				error: function() {    
	            alert('Unable to get truck list of '+$(this).val());
	        	}
	      	});
		} else if(couriertype==2){
			$('#row_sp').show();
			$('#row_plate').hide();
			$('#save_btn').show();
			$('#row_inhouse').hide();
			$("#plateno").empty();
			$('#pickup_driver').hide();
			$('#pickup_plateno').hide();
			$('#row_courier_helper').hide();
			$('#row_courier_helper2').hide();
			$('#courier').val('');
			$('#courier_helper').val('');
			$('#courier_helper2').val('');
			$('#vehicle_type_div').show();
		} else if(couriertype==3){
			$('#row_sp').hide();
			$('#row_inhouse').hide();
			$('#row_plate').hide();
			$('#pickup_driver').show();
			$('#pickup_plateno').show();
			$('#row_courier_helper').hide();
			$('#row_courier_helper2').hide();
			$('#vehicle_type_div').hide();
		}
	}

	function drType(){
		var drtype = $('#drtype').val();
		if(drtype=='DIRECT DELIVERY TO CONSIGNEE'){
			$('#couriertype').find('[value=3]').prop('disabled', true);
			$('#couriertype').selectpicker('refresh');
			$('#direct').show();
			 $('#linehaul').hide();
			 $('#consolidator').val('');
			 $('#transhipment').hide();
		} else if(drtype=='DIRECT DELIVERY TO SP'){
			$('#couriertype').find('[value=3]').prop('disabled', true);
			$('#couriertype').selectpicker('refresh');
			$('#direct').show();
			$('#linehaul').hide();
			$('#transhipment').hide();
		} else if(drtype=='DOMESTIC LINE HAUL TRANSFER'){
			$('#couriertype').find('[value=3]').prop('disabled', false);
			$('#couriertype').selectpicker('refresh');
			$('#linehaul').show();
			$('#direct').hide();
			$('#transhipment').hide();
		} else {
			$('#couriertype').find('[value=3]').prop('disabled', false);
			$('#couriertype').find('[value=2]').prop('disabled', false);
			$('#couriertype').selectpicker('refresh');
			$('#transhipment').show();
			$('#direct').hide();
			$('#linehaul').hide();
		}
		/*get_waybills();*/
	}

	$(document).ready(function() {
		var couriertype=$('#couriertype').val();
		if(couriertype!=''){
			if(couriertype==1){
				$('#row_inhouse').show();
				$('#row_courier_helper').show();
				$('#row_courier_helper2').show();
				$('#row_plate').show();
			} else if(couriertype==2){
				$('#row_sp').show();
				$('#row_plate').show();
			} else if(couriertype==3){
				$('#pickup_driver').show();
				$('#pickup_plateno').show();
			}
		}
		var edit= '<?php echo $_GET['edit']?>';
		
		couriertypes();
		drType();
		
		$('#spcourier').change(function() {
			  var sp_id = $('#spcourier').val();
		  $.ajax({ 
		  		type:"POST", 
		  		url : "delivery_dispatch.php?donotshowheader=1", 
		  		data: {
		    		get_plateno:'2',
		    		sp_id:sp_id
		    	  },
		           success : function(data) {
		           response = JSON.parse(data);
		           $("#plateno").empty();
		           if(response.success===false){
		           } else {
		           		var vehicle_list=response.gtprovider;
						var i;
						for (i = 0; i < vehicle_list.length; i++) {
						  $("#plateno").append(new Option(vehicle_list[i]['plateno'] +' / '+ vehicle_list[i]['provider'],vehicle_list[i]['plateno'] ));
						}
		           }
			},
			error: function() {    
		        alert('Unable to get truck list of '+$(this).val());
		    }
		  });
		});
		});
</script>


