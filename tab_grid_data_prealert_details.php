<?php
$_GET['donotshowheader'] = 1;
include('header.php');
include('session.php');

$sortfield = 'schedulepickupdate ASC';

$queryFilters = '';
if(isset($_GET['acctcodefilter']) && trim($_GET['acctcodefilter'])<>'' && LWLI_CLIENT_ONLINE==ADMIN_USERS_COMPANY) {
  $queryFilters = " AND UPPER(TRIM(tfs.account))=UPPER(TRIM('".FieldDBInput($_GET['acctcodefilter'])."'))";
} elseif(LWLI_CLIENT_ONLINE<>ADMIN_USERS_COMPANY && trim(LWLI_CLIENT_ONLINE)<>'') {
  $queryFilters = " AND UPPER(TRIM(tfs.account))=UPPER(TRIM('".FieldDBInput(LWLI_CLIENT_ONLINE)."'))";
}

if(!isset($_GET['summary_tab']) || $_GET['summary_tab']==2) {
	$queryFilters .=	" AND wtfs.shipment_movement_status=".PRE_ALERTED_STATUS_ID;
} else if(isset($_GET['summary_tab']) && ($_GET['summary_tab']==5)) {
	$queryFilters .=	" AND tfs.billcode_confirmed=1 AND ((wtfs.shipment_movement_status=".SCAN_IN_TO_HUB_STATUS_ID.") OR ".
                    " (wtfs.shipment_movement_status IN (".PRE_ALERTED_STATUS_ID.",".SCAN_IN_TO_HUB_STATUS_ID.",".SHIPMENT_PICKED_UP_STATUS_ID.") AND COALESCE(wtfs.shipment_reference_group_count,0)=0))";
} else if(isset($_GET['summary_tab']) && ($_GET['summary_tab']==9)){
	$queryFilters .=	" AND tfs.billcode_confirmed=0 AND ((wtfs.shipment_movement_status=".SCAN_IN_TO_HUB_STATUS_ID.") OR ".
                    " (wtfs.shipment_movement_status IN (".PRE_ALERTED_STATUS_ID.",".SCAN_IN_TO_HUB_STATUS_ID.",".SHIPMENT_PICKED_UP_STATUS_ID.") AND COALESCE(wtfs.shipment_reference_group_count,0)=0))";
}



  if(isset($_GET['subarea']) && trim($_GET['subarea'])!='') {
	  if(urldecode($_GET['subarea'])==NO_ZIP_CODE_DEFINED) {
      $queryFilters .= " AND (NOT EXISTS (SELECT * FROM sf_zipcode WHERE tfs.zipcode=sf_zipcode.zipcode)) ";
    } else {
      $queryFilters .= " AND (EXISTS (SELECT * FROM sf_zipcode WHERE tfs.zipcode=sf_zipcode.zipcode AND sf_zipcode.subarea='".FieldDBInput(urldecode($_GET['subarea']))."')) ";
    }
  }
 		$filtersummary="";
 	if(isset($_GET['scansummary']) && $_GET['scansummary']==1){
 		$currentdate=date('Y-m-d');
 		$filtersummary=" AND (SUBSTR(wtfs.schedulepickupdate,1,10) <= '".$currentdate."' )";
 	}
 
  if(isset($_GET['area']) && trim($_GET['area'])!='' && trim(strtolower($_GET['area']))!='all') {
     $queryFilters .= "AND (EXISTS (SELECT UPPER(IF(TRIM(COALESCE(tc.csmno,''))='',
	                                        IF(TRIM(COALESCE(tfs.gateway,''))='',
											IF(TRIM(COALESCE(tfs.cityprov,''))='','NO CSM/GATEWAY',TRIM(tfs.cityprov)),
											TRIM(tfs.gateway)),
											IF(TRIM(COALESCE(tc.gateway,''))='' OR
																tc.gateway='SELECT GATEWAY','NO CSM/GATEWAY',TRIM(tc.gateway)))) as areagateway
                                    FROM tf_shipments tfss 
					                          LEFT JOIN web_tf_shipments wtfs on tfss.billed=wtfs.shipment_magic
				                            LEFT JOIN tf_consolidate tc on tc.csmno=tfss.csmno
					                          WHERE (tfss.billed=wtfs.shipment_magic) AND (wtfs.shipment_movement_status IN (".UNDELIVERED_PREALERT_SHIPMENT_STATUS.")) AND (tfss.billed=tfs.billed)
                                    HAVING areagateway='".FieldDBInput(urldecode($_GET['area']))."'))";
  }
  
  if(isset($_GET['remarks']) && trim($_GET['remarks'])!=''){
    $queryFilters .= " AND (EXISTS (SELECT * FROM web_tf_remarks WHERE ((tfs.csmno=web_tf_remarks.csmno) OR (tfs.billed=web_tf_remarks.shipment_magic)) AND web_tf_remarks.remarks_note='".FieldDBInput(trim(urldecode($_GET['remarks'])))."')) ";
  }


if(isset($_GET['spname']) && trim($_GET['spname'])!='') {
  if($_GET['spname']=='NOT ASSIGNED TO SERVICE PARTER')
	  $queryFilters .= " AND COALESCE(tfc.spname,'')='' ";
	else
	  $queryFilters .= " AND COALESCE(tfc.spname,'')='".FieldDBInput($_GET['spname'])."' ";
}
/*if(LWLI_CLIENT_ONLINE=='PRIMER') {

	  $queryFilters .= " AND wtsc.packageno<>'' ";
}*/

$querysqlAll = "SELECT count(*) as numrows FROM tf_shipments tfs
								LEFT JOIN web_tf_shipments wtfs on tfs.billed=wtfs.shipment_magic
								WHERE wtfs.shipment_magic=tfs.billed AND TRIM(COALESCE(tfs.waybillno,''))='' AND wtfs.shipment_movement_status IN (".ALLOWED_ASSIGN_WAYBILL.") ".$queryFilters." ".$filtersummary." ".QueryFilterByDepartment('departmentID','wtfs','AND')." GROUP BY tfs.billed";
$queryAll = doQuery($querysqlAll);
$queryAllRows = mysql_num_rows($queryAll);
$totalData = $queryAllRows;

$querysqlfinal = "SELECT wtfs.shipment_magic as shipment_magic,
												 wtfs.shipment_reference as shipment_reference,
												 wtfs.schedulepickupdate as schedulepickupdate,
												 wtfs.departmentID,
												 wtfs.containerno,
												 tfs.prealertdate as prealertdate,
												 tfs.account as account,
										 		 tfs.waybillno as waybill_no,
												 tfs.orderno as order_number,
                         wtfs.wms_orderno,
												 tfs.consignee as consignee,
												 mfa.name as name,
												 tfs.cityprov as city_province,
												 tfs.street as address,
                         tfs.zipcode,
                         wtfs.schedulepickupdate as schedulepickupdate,
												 wtfs.expecteddeliverydate as expecteddeliverydate,
												 tfs.prealertdate as latestprealert,
												 wtfs.createdDateTime,
												 wtsp.scanned_pickup,
												 wtsp.scanned_rcv,
                         tfs.pickupdate,
                         tfs.rcvdate,
									"."DATEDIFF(IF(tfs.poddate<>'0000-00-00 00:00:00',tfs.poddate,curdate()),IF((ISNULL(tfs.prealertdate)) or (trim(tfs.prealertdate) = '0000-00-00 00:00:00'),pickupdate,tfs.prealertdate)) as TDays
									".$selectfeilds ."
									FROM tf_shipments tfs
									LEFT JOIN mf_accounts mfa on mfa.account=tfs.account
									LEFT JOIN web_tf_shipments wtfs on tfs.billed=wtfs.shipment_magic
									LEFT JOIN web_tf_shipment_package wtsp on wtfs.shipment_magic=wtsp.shipment_magic
									WHERE wtfs.shipment_magic=tfs.billed AND TRIM(COALESCE(tfs.waybillno,''))='' AND wtfs.shipment_movement_status IN (".ALLOWED_ASSIGN_WAYBILL.") ".$queryFilters." ".$filtersummary." ".QueryFilterByDepartment('departmentID','wtfs','AND')." ";

	if(isset($_GET['total_scanin']) && $_GET['total_scanin']==1) {
		if(isset($_GET['acctcodefilter']) && $_GET['acctcodefilter']=='LDS') {
			$columns = array(
				0 => 'wtfs.shipment_reference',
				1 => 'tfs.orderno',
				2 => 'tfs.consignee',
				3 => 'substring(prealertdate,1,10)',
				4 => 'TDays',
				5 => 'schedulepickupdate',
				6 => 'expecteddeliverydate',
				7 => 'tfs.street',
				8 => 'tfs.cityprov'
			);
		} else {
			$columns = array(
				0 => 'tfs.orderno',
				1 => 'tfs.consignee',
				2 => 'substring(prealertdate,1,10)',
				3 => 'TDays',
				4 => 'wtfs.schedulepickupdate',
				5 => 'wtfs.expecteddeliverydate',
				6 => 'tfs.street',
				7 => 'tfs.cityprov'
			);
		}


	 $filtercondition = 'AND';
	if($queryFilters<>'')
	 $filtercondition = ' AND ';
		$searchstring = FieldDBInput($_REQUEST['search']['value']);
		$querysearchfilter = "";

		if(trim($searchstring)<>'') {    // if there is a search parameter, $requestData['search']['value'] contains search parameter
		  $querysearchfilter .= $filtercondition;
			$querysearchfilter.=" ( (wtfs.shipment_reference LIKE '".$searchstring."%') ";
			$querysearchfilter.=" OR (tfs.orderno LIKE '".$searchstring."%') ";
			$querysearchfilter.=" OR (tfs.consignee LIKE '".$searchstring."%') ";
			$querysearchfilter.=" OR (DATE_FORMAT(prealertdate ,'%d-%b-%y') LIKE '".$searchstring."%')";
			$querysearchfilter.=" OR (TDays LIKE '".$searchstring."%') ";
			$querysearchfilter.=" OR (DATE_FORMAT(wtfs.schedulepickupdate ,'%d-%b-%y') LIKE '".$searchstring."%')";
			$querysearchfilter.=" OR (DATE_FORMAT(wtfs.expecteddeliverydate ,'%d-%b-%y') LIKE '".$searchstring."%')";
			$querysearchfilter.=" OR (tfs.street LIKE '".$searchstring."%') ";
			$querysearchfilter.=" OR (tfs.cityprov LIKE '".$searchstring."%') ";
			$querysearchfilter.= ") ";
		 }
	} else {
	  if(isset($_GET['acctcodefilter']) && $_GET['acctcodefilter']=='LDS') {
			$columns = array(
				0 => 'wtfs.shipment_reference',
				1 => 'tfs.orderno',
				2 => 'tfs.consignee',
				3 => 'substring(prealertdate,1,10)',
				4 => 'TDays',
				5 => 'wtfs.schedulepickupdate',
				6 => 'wtfs.expecteddeliverydate',
				7 => 'tfs.cityprov',
				8 => 'tfs.street'
			);
		} else {
			$columns = array(
				0 => 'tfs.orderno',
				1 => 'tfs.consignee',
				2 => 'substring(prealertdate,1,10)',
				3 => 'TDays',
				4 => 'wtfs.schedulepickupdate',
				5 => 'wtfs.expecteddeliverydate',
				6 => 'tfs.cityprov',
				7 => 'tfs.street'
			);
		}


	$filtercondition = 'AND';
	if($queryFilters<>'')
	 $filtercondition = ' AND ';
		$searchstring = FieldDBInput($_REQUEST['search']['value']);
		$querysearchfilter = "";

		if(trim($searchstring)<>'') {    // if there is a search parameter, $requestData['search']['value'] contains search parameter
			$querysearchfilter.=" ((wtfs.shipment_reference LIKE '".$searchstring."%') ";
			$querysearchfilter.=" OR (tfs.orderno LIKE '".$searchstring."%') ";
			$querysearchfilter.=" OR (tfs.consignee LIKE '".$searchstring."%') ";
			$querysearchfilter.=" OR (DATE_FORMAT(prealertdate ,'%d-%b-%y') LIKE '".$searchstring."%')";
			$querysearchfilter.=" OR (TDays LIKE '".$searchstring."%') ";
			$querysearchfilter.=" OR (DATE_FORMAT(wtfs.schedulepickupdate ,'%d-%b-%y') LIKE '".$searchstring."%')";
			$querysearchfilter.=" OR (DATE_FORMAT(wtfs.expecteddeliverydate ,'%d-%b-%y') LIKE '".$searchstring."%')";
			$querysearchfilter.=" OR (tfs.street LIKE '".$searchstring."%') ";
			$querysearchfilter.=" OR (tfs.cityprov LIKE '".$searchstring."%') ";
			$querysearchfilter.= ") ";
		 }
	}

	$querysqlfinal .= ' GROUP BY tfs.waybillno,tfs.billed '.(($querysearchfilter!='') ? 'HAVING '.$querysearchfilter : '');

$query=doQuery($querysqlfinal);
$totalFiltered = mysql_num_rows($query); // when there is a search parameter then we have to modify total number filtered rows as per search result.

$orderby = '';
for($xx = 0; $xx < sizeof($_REQUEST['order']); $xx++) {
  if($orderby<>'')
    $orderby .= ',';
  $orderby .= $columns[(int)$_REQUEST['order'][$xx]['column']]." ".$_REQUEST['order'][$xx]['dir'];
}
if($orderby<>'')
  $orderby = " ORDER BY ".$orderby;

$limitoffset = " LIMIT ".(int)$_REQUEST['start'].", ".(int)$_REQUEST['length'];
if((int)$_REQUEST['start'] == 0 && (int)$_REQUEST['length'] == 0)
  $limitoffset = '';

$querysqlfinal.= $orderby . $limitoffset;
$query=doQuery($querysqlfinal);

$data = array();
while($row=mysql_fetch_array($query)) {  // preparing an array
	$nestedData=array();

		if((IsAdmin() || IsClientAdmin()) && ($_GET['summary_tab']==3 || $_GET['summary_tab']==5 || $_GET['summary_tab']==0 || $_GET['summary_tab']==9)) {
			$assignwaybill = '<td><a title="Print Waybill" href="javascript:ShowWaybillAssign(\'assign_waybill.php?shipment_magic='.$row['shipment_magic'].'&pickupanddeliver='.(($_GET['summary_tab']==0) ? 1 : '').'\',\'Assign Waybill on Shipment/Order#: <strong><u>'.$row['order_number'].'</u></strong>\')"><i class="icon-edit icon-large"></i> </a></td>';
			if($row['waybill_no']==''){
				$assignCaption  = ($_GET['summary_tab']==3 || $_GET['summary_tab']==5 || $_GET['summary_tab']==0) ? 'Assign Waybill' : 'Edit Billing Code';
					$nestedData[] = '<table class="statusdetails"><tr>'.$assignwaybill.'<td><p align="left" style="color:red;"><small>'.$assignCaption.'</small></p></td></tr></table>';
					$nestedData[] = '';
			} else {
				$nestedData[] = '	<table class="statusdetails"><tr><td><a title="Print Waybill" href="javascript:ShowTransDetails(\'waybill_PDF.php?shipment_magic='.$row['shipment_magic'].'\',\'Details of Order#'.$row['order_number'].'\')"><i class="icon-print icon-large"></i></a></td><td></td></tr></table>';
			}
		} else {
			$showpackages='<a title="Print Waybill" href="javascript:ShowWaybillAssign(\'assign_waybill.php?shipment_magic='.$row['shipment_magic'].'\',\'Assign Waybill on Shipment/Order#: <strong><u>'.$row['order_number'].'</u></strong>\')"><i class="icon-edit icon-large"></i> </a>';
			if($row['wtsp.scanned_pickup']==1 && $row['wtsp.scanned_rcv']==1){
				$status_scan='Finished';
			} else if($row['wtsp.scanned_pickup']==1 && $row['wtsp.scanned_rcv']==0){
				$status_scan='Partial';
			} else if($row['wtsp.scanned_pickup']==0 && $row['wtsp.scanned_rcv']==0){
				$status_scan='<a title="" href="javascript:ShowPackagesStatus(\'tab_mngt_package_status.php?shipment_magic='.$row['shipment_magic'].'\',\' Order Number: '.$row['order_number'].'\')">Pending </a>';
			} else if($row['wtsp.scanned_pickup']==0 && $row['wtsp.scanned_rcv']==1){
				$status_scan='Scanned';
			}

			$checkscanned=doQuery("SELECT SUM(IF(scanned_pickup=1,'1','0')) as pickup_scanned,
                                    SUM(IF(scanned_pickup=0,'1','0')) as pickup_pending,
                                    SUM(IF(scanned_rcv=1,'1','0')) as rcv_scanned,
                                    SUM(IF(scanned_rcv=0,'1','0')) as rcv_pending FROM web_tf_shipment_package where shipment_magic='".$row['shipment_magic']."'");
      $rowStatus=mysql_fetch_array($checkscanned);
      
			if($rowStatus['pickup_scanned'] > 0 && $rowStatus['pickup_pending'] > 0){
				$scanStatuspickup='Partial';
      } else if($rowStatus['pickup_pending'] == 0) {
        $scanStatuspickup='Finished';
      } else {
        $scanStatuspickup='Pending';
      }
				
      if($rowStatus['rcv_scanned'] > 0 && $rowStatus['rcv_pending'] > 0){
				$scanStatusrcv='Partial';
      } else if($rowStatus['rcv_pending'] == 0) {
        $scanStatusrcv='Finished';
      } else {
        $scanStatusrcv='Pending';
      }
      
      if($rowStatus['rcv_pending'] == 0 && $rowStatus['pickup_pending'] == 0) {
        $nestedData = array();
        CONTINUE;
      }
			
      
			$nestedData[] = '<a title="" href="javascript:ShowPackagesStatus(\'tab_mngt_package_status.php?shipment_magic='.$row['shipment_magic'].'&scan=1\',\' Order Number: '.$row['order_number'].'\')"> '.$scanStatuspickup.' </a>';

			$nestedData[] = '<a title="" href="javascript:ShowPackagesStatus(\'tab_mngt_package_status.php?shipment_magic='.$row['shipment_magic'].'&scan=2\',\' Order Number: '.$row['order_number'].'\')"> '.$scanStatusrcv.' </a>';
		}

		if(isset($_GET['total_scanin']) && $_GET['total_scanin']==1) {
	   	$nestedData[] = $row['account'];
		}
    
    	//$nestedData[] = '<a title="Edit Transaction Details" href="javascript:ShowTransDetails(\'edit_shipment.php?shipment_magic='.$row['shipment_magic'].'&trip_number='.$row['trip_number'].'\',\'DETAILS OF ORDER NUMBER : '.$row['order_number'].'\')">
	                 //'.$row['order_number'].'</a>';
		$nestedData[] = $row['order_number'];
                   
		if($_SESSION['AccountCode']=='LDS') {
	    $nestedData[] = $row['shipment_reference'];
      $nestedData[] = $row['wms_orderno'];
    }
	  
		
	
		
	  $nestedData[] = $row['consignee'];
		$nestedData[] = FormatDate($row['createdDateTime'],5);
		$nestedData[] = $row['TDays'];
    if($_GET['summary_tab']==5) {
      $nestedData[] = FormatDate($row['pickupdate'],5);
      $nestedData[] = FormatDate($row['rcvdate'],5);
    }
		if(LWLI_CLIENT_ONLINE!='PRIMER') {
			if($_GET['summary_tab']!=5) {
         $nestedData[] = FormatDate($row['schedulepickupdate'],5);
      }
			$nestedData[] = FormatDate($row['expecteddeliverydate'],5);
		}
		$nestedData[] = $row['city_province'];
		$nestedData[] = $row['address'];
    $nestedData[] = $row['zipcode'];
		
	
 	  $data[] = $nestedData;
}

$json_data = array(
			"draw"            => intval( $_REQUEST['draw'] ),   // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
			"recordsTotal"    => intval( $totalData ),  // total number of records
			"recordsFiltered" => intval( $totalFiltered ), // total number of records after searching, if there is no searching then totalFiltered = totalData
			"data"            => $data   // total data array
			);

echo json_encode($json_data);
?>
