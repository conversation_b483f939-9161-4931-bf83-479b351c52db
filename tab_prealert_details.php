<style>
	#prealertReport{
	text-transform: uppercase;
}
<?php
$fastrvieweport = 0;
if(isset($_GET['subarea']) && isset($_GET['area'])) {
	$fastrvieweport = 1;
}
?>
</style>
<?php /* if($fastrvieweport==0) { ?>
<div class="row">
	<div class='col-sm-4'>
		<select class="form-control" id="sel1">
		  <option value="">ALL SERVICE PARTNER</option>
		  <option value="NOT ASSIGNED TO SERVICE PARTER"<?php echo (isset($_GET['spname']) && $_GET['spname']=='NOT ASSIGNED TO SERVICE PARTER') ? " selected " : "" ?>>NOT ASSIGNED TO SERVICE PARTNER</option>
		  <?php
			  $query=doQuery("SELECT TRIM(UPPER(spname)) as spname FROM mf_partners");
				while($row=mysql_fetch_array($query)){
				?>
			  <option value="<?php echo $row['spname'] ?>"<?php echo (isset($_GET['spname']) && $_GET['spname']==$row['spname']) ? " selected " : "" ?>><?php echo $row['spname'] ?></option>
		   <?php } ?>
		</select>
  </div>
</div>

<br/>
<?php } */ ?>
<div class="tab-pane fade in active" id="tabedit">
	   <table class="table table-hover table-bordered" style="width:100%" id="prealertreport">
         <thead>
         	
			
			<?php if(isset($_GET['scansummary'])) {  ?>
         	
			<tr id="prealertReport">
			<th colspan=2 align="center"><center>SCAN STATUS</center></th>
		    <th rowspan=2>Order Number</th>	
			<?php if($_SESSION['AccountCode']=='LDS') { ?>
			<th rowspan=2>DHL Shipment ID</th>
			<th rowspan=2>WMS Order Number</th>
			<?php } ?>
			<th rowspan=2>Consignee</th>
			<th rowspan=2>Pre-alert Date</th>
			<th rowspan=2>Aging</th>
			<th rowspan=2>Schedule Pick up Date</th>
			<th rowspan=2>Expected Delivery Date</th>
			<th rowspan=2>City/Province</th>
			<th rowspan=2>Address</th>
			<th rowspan=2>Zipcode</th>
		   </tr>
			<tr>
			<th>Pickup</th>
			<th>Receive</th>
			</tr>
			
			<?php } else { ?>
			<?php $showColspan='';
				  $captionFirstCol='Waybill';
				  if($_REQUEST['showtab']==9) { 
					$showColspan=' colspan="2" align="center"'; 
					$captionFirstCol='Billing Code';
				 } 
			?>
			<tr id="prealertReport">
			<?php if(IsAdmin() || IsClientAdmin()) { ?>
			<th <?php echo $showColspan; ?>><?php echo $captionFirstCol; ?></th>
			<?php } ?>
			<?php if(isset($_GET['total_scanin']) && $_GET['total_scanin']==1) { ?>
			<th rowspan=2>Account</th>
			<?php } ?>
			<th rowspan=2>Order Number</th>	
			<?php if($_SESSION['AccountCode']=='LDS') { ?>
			<th rowspan=2>DHL Shipment ID</th>
			<th>WMS Order Number</th>
			<?php } ?>
			<th rowspan=2>Consignee</th>
			<th rowspan=2>Pre-alert Date</th>
			<th rowspan=2>Aging</th>
			<?php if($_GET['showtab']==5) { ?>
			<th rowspan=2>Date Pickup</th>
			<th rowspan=2>Date Received</th>
			<?php } ?>
			<?php if(LWLI_CLIENT_ONLINE!='PRIMER') { ?>
			<?php if($_GET['showtab']!=5) { ?>
			<th rowspan=2>Schedule Pick up Date</th>
			<?php } ?>
			<th rowspan=2>Expected Delivery Date</th>
			<?php } ?>
			<th rowspan=2>City/Province</th>
			<th rowspan=2>Address</th>
		   </tr>
		   <tr>
		   <?php if(IsAdmin() || IsClientAdmin()) { ?>
		   <th>Edit</th>
		   <th>Confirm</th>
		   <?php } ?>
		   </tr>
		   <?php } ?>
		</thead>
	 </table>

	<script type="text/javascript">
        var dataTable = $('#prealertreport').DataTable({
			"scrollY": true,
			"scrollX": true,			
			"processing": false,
			"serverSide": true,
			<?php if(isset($_GET['total_scanin']) && $_GET['total_scanin']==1) { ?>
				"order": [[4, "desc"], [1, "asc"], [0, "asc"]],
			<?php } else {
				if(isset($_GET['acctcodefilter']) && $_GET['acctcodefilter']=='LDS') { ?>
					"order": [[3, "asc"],[5, "asc"], [0, "asc"], [1, "asc"]],
			<?php } else { ?>
					"order": [[2, "asc"],[4, "asc"], [0, "asc"], [1, "asc"]],
			<?php }
			} ?>
			"pageLength": 50,
			"ajax":{
				url :"tab_grid_data_prealert_details.php?subarea=<?php echo urlencode($_GET['subarea']) ?>&area=<?php echo urlencode($_GET['area']) ?>&remarks=<?php echo urlencode($_GET['remarks']) ?>&from=<?php echo urlencode($_GET['from']) ?>&to=<?php echo urlencode($_GET['to']) ?>&spname=<?php echo $_REQUEST['spname'] ?>&summary_tab=<?php echo $_REQUEST['showtab'] ?>&<?php echo ((isset($_GET['acctcodefilter'])) ? 'acctcodefilter='.$_GET['acctcodefilter'].'&' : 'acctcodefilter=&') . (isset($_GET['total_scanin']) ? 'total_scanin='.$_GET['total_scanin'] : 'total_scanin=') ?>&scansummary=<?php echo $_GET['scansummary'] ?>",
				type: "post",
				error: function(){
					$(".prealertreport-error").html("");
					$("#prealertreport_processing").css("display","none");
				}
			}
		});
		
		
		
		setInterval(function() { dataTable.ajax.reload(null, false); }, 300000);
		
		$(document).ready(function() {
		   // GetShipmentData(0);
           $("#sel1").change(function(){
                        window.location = "prealert_report.php?spname="+$("#sel1").val()+"&showtab=<?php echo $_REQUEST['showtab'] ?>&<?php echo ((isset($_GET['acctcodefilter'])) ? 'acctcodefilter='.$_GET['acctcodefilter'].'&' : 'acctcodefilter=&') . (isset($_GET['show_noprealert']) ? 'show_noprealert='.$_GET['show_noprealert'] : 'show_noprealert=') ?>";
                  });
			
		
		
		
		});
			
			
			function ShowTransDetails(eurl,etitle) {
					  eModal.iframe(eurl,etitle);
				}
					
		    function ShowWaybillAssign(eurl,etitle) {
					  params = {url: eurl, size: 'sm'}
					  eModal.iframe(params,etitle);			
				}
			function ShowContainerAssign(eurl,etitle) {
					  params = {url: eurl, size: 'sm'}
					  eModal.iframe(params,etitle);			
				}
			function ShowPackagesStatus(eurl,etitle) {
					  params = {url: eurl, size: 'md'}
					  eModal.iframe(params,etitle);			
				}	
			
		
			   	
		//-->
    </script>

	</div>
  
